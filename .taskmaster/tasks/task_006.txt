# Task ID: 6
# Title: RBAC/ABAC Permission System with CASL
# Status: in-progress
# Dependencies: 5
# Priority: high
# Description: Implement a flexible role-based and attribute-based access control system using CASL. This update focuses on detailing the implementation requirements, including addressing the `TODO("實現完整的權限邏輯")` in `auth.service.ts` by fully implementing the `getUnifiedUserPermissions` method, extending CASL rules for different user types, and ensuring robust permission checks in controllers.
# Details:
Integrate `@casl/ability 6+` and `@casl/prisma 1+` for permission management.
Key implementation aspects include:
1.  **Defining abilities** for different user roles: `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`, with specific attention to **extending CASL rules to support distinct permission requirements for `SystemUser` and `TenantUser`**.
2.  **Fully implementing the `getUnifiedUserPermissions` method** in `auth.service.ts` to retrieve unified user permissions, addressing the `TODO("實現完整的權限邏輯")`.
3.  Creating `PermissionsGuard` to check abilities before controller actions and implementing permission decorators.
4.  Implementing services for role assignment and management.
5.  **Providing detailed implementation for both role-based and attribute-based permission checks**, including resource-based permissions using `@casl/prisma`.
6.  **Updating permission-related endpoints in `auth.controller.ts`** to use the new permission logic and guards.

# Test Strategy:
Test all permission combinations, verify role inheritance works correctly, and ensure unauthorized access is properly blocked. **Specifically, test the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` vs. `TenantUser`, the behavior of role-based and attribute-based checks, and the security of updated `auth.controller.ts` endpoints.**

# Subtasks:
## 1. CASL Ability Definitions and Role Hierarchy [done]
### Dependencies: None
### Description: Define user abilities and establish a role hierarchy using CASL to manage permissions effectively, including specific rules for SystemUser and TenantUser.
### Details:
Utilize CASL's `AbilityBuilder` to define user abilities for roles like `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`. **Crucially, extend CASL rules to support the distinct permission requirements for `SystemUser` and `TenantUser` roles.** Establish a role hierarchy. This work will contribute to resolving the `TODO("實現完整的權限邏輯")` in `auth.service.ts` concerning permission definitions. ([npmjs.com](https://www.npmjs.com/package/%40casl/ability?utm_source=openai))

## 2. Permission Guard Implementation and Decorators [done]
### Dependencies: 6.1
### Description: Implement permission guards and decorators to enforce access control, with detailed logic for role-based checks.
### Details:
Develop `PermissionsGuard` to check CASL abilities before controller actions. Create permission decorators for easy controller protection. **Provide detailed implementation for robust role-based permission checks within the guard and decorators.** This involves creating middleware to validate tenant context if applicable and using decorators to specify required permissions for routes. ([medium.com](https://medium.com/%40taofiqaiyelabegan/building-a-multi-tenant-e-commerce-api-fine-grained-authorization-with-nest-js-and-permit-io-ddd2ca77d19d?utm_source=openai))
<info added on 2025-06-11T15:40:57.785Z>
開始實作 Permission Guard Implementation and Decorators。

已完成專案架構分析：
- 現有 PoliciesGuard 基本功能完整，但需要增強錯誤處理和日誌記錄
- @CheckPolicies 裝飾器已實作，但需要更多便利的裝飾器
- CaslAbilityFactory 和 getUnifiedUserPermissions 已完整實作
- 需要實作租戶上下文驗證機制

下一步將：
1. 增強 PoliciesGuard 的錯誤處理和日誌記錄
2. 建立便利的權限裝飾器
3. 實作租戶上下文驗證
4. 建立權限檢查工具
</info added on 2025-06-11T15:40:57.785Z>
<info added on 2025-06-11T15:48:12.521Z>
完成的功能：
1. 增強的 PoliciesGuard - 改善了錯誤處理、日誌記錄和性能監控
2. 便利的權限裝飾器 - 建立了 @RequirePermissions、@RequireRead、@RequireCreate 等便利裝飾器
3. 租戶上下文驗證 - 實作了 TenantContextMiddleware 和相關裝飾器確保多租戶隔離
4. 權限檢查工具 - 建立了 PermissionCheckerService 提供程式化權限檢查
5. 統一權限守衛 - 實作了 UnifiedPermissionGuard 整合所有功能
6. 完整的測試 - 建立了單元測試確保功能正確性
7. 使用範例 - 提供了詳細的使用範例和文檔

技術實作：
- 支援 @CheckPolicies 和 @RequirePermissions 兩種裝飾器
- 自動租戶上下文驗證和資源隔離
- 詳細的權限檢查日誌和錯誤處理
- 條件變數替換（{{userId}}, {{tenantId}} 等）
- 批量權限檢查和性能最佳化
- 完整的 TypeScript 類型安全

已更新的檔案：
- 增強 PoliciesGuard
- 新增便利權限裝飾器
- 新增租戶上下文中介軟體
- 新增權限檢查服務
- 新增統一權限守衛
- 更新 CASL 模組和應用程式模組
- 建立完整的文檔和範例

系統現在提供了強健、靈活且易用的權限管理機制！
</info added on 2025-06-11T15:48:12.521Z>

## 3. Role Assignment and Management Services [done]
### Dependencies: 6.1
### Description: Create services to assign and manage user roles within the application.
### Details:
Develop services to assign and manage user roles, ensuring that users have appropriate permissions based on their roles. This includes creating functions to assign roles to users and manage role hierarchies. ([codeproject.com](https://www.codeproject.com/Articles/151527/Access-Control-in-Multi-Tenant-Applications-with-V?utm_source=openai))
<info added on 2025-06-11T16:36:30.992Z>
現狀分析完成：

現有的角色管理基礎設施：
資料庫結構完整：
- roles 表：支援角色層級、範圍（SYSTEM/TENANT/WORKSPACE）
- system_user_roles 表：系統用戶角色關聯
- tenant_user_roles 表：租戶用戶角色關聯
- role_permissions 表：角色權限關聯

現有服務：
- RolesService：基本的角色 CRUD 操作
- SystemUserService：系統用戶管理
- TenantUserService：租戶用戶管理

需要實作的功能：
用戶角色指派服務（支援 SystemUser 和 TenantUser）
角色層級管理（父子角色關係）
批量角色操作
角色權限同步
角色變更歷史記錄
角色驗證和衝突檢查

實作計劃：
1. 建立 RoleAssignmentService - 統一的角色指派管理
2. 建立 RoleHierarchyService - 角色層級管理
3. 建立 UserRoleService - 用戶角色關聯管理
4. 整合現有的 CASL 權限系統
5. 建立完整的測試和範例
</info added on 2025-06-11T16:36:30.992Z>
<info added on 2025-06-11T16:44:23.228Z>
開始實作任務 6.3: Role Assignment and Management Services

現狀分析完成：

現有的角色管理基礎設施：
✅ 資料庫結構完整：
- roles 表：支援角色層級、範圍（SYSTEM/TENANT/WORKSPACE）
- system_user_roles 表：系統用戶角色關聯
- tenant_user_roles 表：租戶用戶角色關聯
- role_permissions 表：角色權限關聯

✅ 現有服務：
- RolesService：基本的角色 CRUD 操作
- SystemUserService：系統用戶管理
- TenantUserService：租戶用戶管理

需要實作的功能：
🔧 用戶角色指派服務（支援 SystemUser 和 TenantUser）
🔧 角色層級管理（父子角色關係）
🔧 批量角色操作
🔧 角色權限同步
🔧 角色變更歷史記錄
🔧 角色驗證和衝突檢查

實作計劃：
1. 建立 RoleAssignmentService - 統一的角色指派管理
2. 建立 RoleHierarchyService - 角色層級管理
3. 建立 UserRoleService - 用戶角色關聯管理
4. 整合現有的 CASL 權限系統
5. 建立完整的測試和範例
</info added on 2025-06-11T16:44:23.228Z>
<info added on 2025-06-11T16:53:45.745Z>
✅ 完成的功能：

1. **RoleAssignmentService** - 核心角色指派服務
   - 支援角色指派、移除、替換操作
   - 批量角色操作功能
   - 角色衝突檢測和驗證
   - 完整的錯誤處理和日誌記錄

2. **RoleHierarchyService** - 角色層級管理服務
   - 父子角色關係管理
   - 權限繼承機制
   - 角色層級驗證和循環檢測
   - 層級結構完整性檢查

3. **UserRoleService** - 用戶角色管理服務
   - 用戶有效權限計算（包含繼承權限）
   - 權限檢查功能（單一、批量、任一、全部）
   - 用戶查詢功能（按範圍、角色、權限）
   - 權限快取同步機制

4. **完整的 API 控制器**
   - RoleAssignmentController 提供 RESTful API
   - 支援所有角色管理操作
   - 完整的 Swagger 文檔
   - CASL 權限保護

5. **類型安全的 DTO**
   - 完整的請求驗證
   - 類型安全的介面定義
   - API 文檔自動生成

6. **完整的測試覆蓋**
   - 單元測試
   - 錯誤情況測試
   - 邊界條件測試

7. **實用的範例和文檔**
   - 詳細的使用文檔
   - 完整的範例程式碼
   - 最佳實踐指南

🔧 技術特色：
- 支援 SystemUser 和 TenantUser 的不同角色體系
- 與 CASL 權限系統完全整合
- 自動租戶隔離和驗證
- 角色衝突檢測和防護
- 批量操作和效能最佳化
- 完整的審計記錄

📋 已建立的檔案：
- interfaces/role-assignment.interface.ts - 類型定義
- services/role-assignment.service.ts - 核心指派服務
- services/role-hierarchy.service.ts - 層級管理服務
- services/user-role.service.ts - 用戶角色服務
- controllers/role-assignment.controller.ts - API 控制器
- dto/role-assignment.dto.ts - 請求驗證 DTO
- 測試檔案和使用範例
- 完整的文檔和 README

系統現在提供了企業級的角色指派和管理功能！
</info added on 2025-06-11T16:53:45.745Z>

## 4. Resource-Based Permission Checking [pending]
### Dependencies: 6.1
### Description: Implement resource-based permission checks, including detailed attribute-based logic, to control access to specific resources.
### Details:
Implement resource-based permission checks to control access to specific resources. **Provide detailed implementation for attribute-based permission checks, leveraging `@casl/prisma` where appropriate.** This involves defining permissions for individual resources and checking user abilities against resource attributes before granting access. ([fullstack.com](https://www.fullstack.com/labs/resources/blog/role-based-user-authorization-in-javascript-with-casl?utm_source=openai))

## 5. Permission Testing and Validation Framework [pending]
### Dependencies: 6.1, 6.2, 6.4, 6.6, 6.7
### Description: Develop a framework to test and validate permission configurations, covering new methods and specific user type scenarios.
### Details:
Develop a framework to test and validate permission configurations. **Include test cases for the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` and `TenantUser`, comprehensive role-based and attribute-based checks, and the updated `auth.controller.ts` endpoints.** Ensure that access control rules are correctly enforced and unauthorized access is blocked. ([dev.to](https://dev.to/samueloseh/mastering-role-based-access-control-with-casl-part-one-5d1a?utm_source=openai))

## 6. Implement `getUnifiedUserPermissions` in `auth.service.ts` [done]
### Dependencies: 6.1
### Description: Fully implement the `getUnifiedUserPermissions` method within `auth.service.ts` to retrieve unified user permissions, addressing a key part of the `TODO("實現完整的權限邏輯")`.
### Details:
This method should aggregate permissions based on user roles (including `SystemUser`, `TenantUser`), direct assignments, and other relevant attributes to construct a comprehensive CASL `Ability` instance for the user. Ensure it correctly reflects the defined CASL rules and hierarchy.

## 7. Update `auth.controller.ts` Endpoints with New Permission Logic [pending]
### Dependencies: 6.2, 6.6
### Description: Integrate the new CASL permission system into `auth.controller.ts` by applying guards and decorators to relevant endpoints.
### Details:
Refactor existing permission-related endpoints in `auth.controller.ts`. Apply the `PermissionsGuard` and appropriate permission decorators to protect these endpoints. Ensure that the controller actions correctly leverage the CASL abilities provided by the updated `auth.service.ts`.

