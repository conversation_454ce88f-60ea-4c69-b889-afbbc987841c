#!/usr/bin/env node

/**
 * Refresh Token 功能測試腳本
 * 
 * 這個腳本可以用來測試 refresh token 功能是否正常工作
 * 使用方法：node scripts/test-refresh-token.js
 */

const axios = require('axios');

// 配置
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'admin123';

console.log('🧪 開始測試 Refresh Token 功能...');
console.log(`📍 API 基礎 URL: ${BASE_URL}`);

async function testRefreshToken() {
  try {
    // 1. 登入獲取 tokens
    console.log('\n1️⃣ 嘗試登入...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    }, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (loginResponse.status === 200) {
      console.log('✅ 登入成功');
      console.log(`👤 用戶: ${loginResponse.data.user.email}`);
      
      // 檢查是否有 Set-Cookie 標頭
      const setCookieHeader = loginResponse.headers['set-cookie'];
      if (setCookieHeader) {
        console.log('🍪 收到 cookies:', setCookieHeader);
        
        // 檢查是否包含 auth_token 和 refresh_token
        const hasAuthToken = setCookieHeader.some(cookie => cookie.includes('auth_token='));
        const hasRefreshToken = setCookieHeader.some(cookie => cookie.includes('refresh_token='));
        
        if (hasAuthToken && hasRefreshToken) {
          console.log('✅ 登入時正確設置了 auth_token 和 refresh_token cookies');
        } else {
          console.log('⚠️  登入時缺少必要的 cookies');
          console.log(`   auth_token: ${hasAuthToken ? '✅' : '❌'}`);
          console.log(`   refresh_token: ${hasRefreshToken ? '✅' : '❌'}`);
        }
      } else {
        console.log('⚠️  未收到 cookies');
      }
    } else {
      throw new Error(`登入失敗: ${loginResponse.status}`);
    }

    // 2. 測試 refresh token
    console.log('\n2️⃣ 嘗試刷新 token...');
    
    // 從登入回應中提取 cookies
    const cookies = loginResponse.headers['set-cookie'];
    let cookieHeader = '';
    if (cookies) {
      cookieHeader = cookies.map(cookie => cookie.split(';')[0]).join('; ');
    }

    const refreshResponse = await axios.post(`${BASE_URL}/api/auth/refresh-token`, {}, {
      withCredentials: true,
      headers: {
        'Cookie': cookieHeader,
        'Content-Type': 'application/json'
      }
    });

    if (refreshResponse.status === 200) {
      console.log('✅ Token 刷新成功');
      console.log('📄 回應:', refreshResponse.data);
      
      // 檢查是否有新的 cookies
      const newSetCookieHeader = refreshResponse.headers['set-cookie'];
      if (newSetCookieHeader) {
        console.log('🍪 收到新的 cookies:', newSetCookieHeader);
        
        // 檢查是否包含新的 auth_token 和 refresh_token
        const hasNewAuthToken = newSetCookieHeader.some(cookie => cookie.includes('auth_token='));
        const hasNewRefreshToken = newSetCookieHeader.some(cookie => cookie.includes('refresh_token='));
        
        if (hasNewAuthToken && hasNewRefreshToken) {
          console.log('✅ Token 刷新時正確設置了新的 cookies');
        } else {
          console.log('⚠️  Token 刷新時缺少新的 cookies');
          console.log(`   new auth_token: ${hasNewAuthToken ? '✅' : '❌'}`);
          console.log(`   new refresh_token: ${hasNewRefreshToken ? '✅' : '❌'}`);
        }
      } else {
        console.log('⚠️  Token 刷新時未收到新的 cookies');
      }
    } else {
      throw new Error(`Token 刷新失敗: ${refreshResponse.status}`);
    }

    // 3. 測試使用新的 token 存取需要認證的端點
    console.log('\n3️⃣ 測試使用新 token 存取認證端點...');
    
    // 使用新的 cookies
    const newCookies = refreshResponse.headers['set-cookie'];
    let newCookieHeader = '';
    if (newCookies) {
      newCookieHeader = newCookies.map(cookie => cookie.split(';')[0]).join('; ');
    }

    try {
      const meResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
        withCredentials: true,
        headers: {
          'Cookie': newCookieHeader
        }
      });
      
      if (meResponse.status === 200) {
        console.log('✅ 使用新 token 成功存取認證端點');
        console.log('👤 當前用戶:', meResponse.data.user.email);
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('❌ 使用新 token 無法存取認證端點');
      } else {
        console.log('⚠️  測試認證端點時發生未預期的錯誤:', error.message);
      }
    }

    // 4. 測試無效的 refresh token
    console.log('\n4️⃣ 測試無效的 refresh token...');
    
    try {
      const invalidRefreshResponse = await axios.post(`${BASE_URL}/api/auth/refresh-token`, {}, {
        withCredentials: true,
        headers: {
          'Cookie': 'refresh_token=invalid_token_12345',
          'Content-Type': 'application/json'
        }
      });
      
      console.log('❌ 無效的 refresh token 應該被拒絕，但卻成功了');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 無效的 refresh token 正確被拒絕');
      } else {
        console.log('⚠️  測試無效 refresh token 時發生未預期的錯誤:', error.message);
      }
    }

    console.log('\n🎉 Refresh Token 功能測試完成！');

  } catch (error) {
    console.error('\n❌ 測試過程中發生錯誤:');
    
    if (error.response) {
      console.error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      console.error('回應內容:', error.response.data);
    } else if (error.request) {
      console.error('無法連接到伺服器:', error.message);
    } else {
      console.error('錯誤:', error.message);
    }
    
    process.exit(1);
  }
}

// 執行測試
testRefreshToken().catch(error => {
  console.error('測試執行失敗:', error);
  process.exit(1);
});
