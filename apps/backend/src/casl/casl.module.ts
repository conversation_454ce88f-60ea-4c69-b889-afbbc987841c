import { Module, Global } from '@nestjs/common';
import { PrismaModule } from '../modules/core/prisma/prisma.module';
import { CaslAbilityFactory } from './casl-ability.factory';
import { PermissionCheckerService } from './services/permission-checker.service';
import { PoliciesGuard } from './guards/permission.guard';
import { EnhancedPermissionGuard } from './guards/enhanced-permission.guard';
import { UnifiedPermissionGuard } from './guards/unified-permission.guard';
import { TenantContextMiddleware } from './middleware/tenant-context.middleware';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [
    CaslAbilityFactory,
    PermissionCheckerService,
    PoliciesGuard,
    EnhancedPermissionGuard,
    UnifiedPermissionGuard,
    TenantContextMiddleware,
  ],
  exports: [
    CaslAbilityFactory,
    PermissionCheckerService,
    PoliciesGuard,
    EnhancedPermissionGuard,
    UnifiedPermissionGuard,
    TenantContextMiddleware,
  ],
})
export class CaslModule {}