import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { Request } from 'express';
import {
  UnifiedPermissionGuard,
  RequireRead,
  RequireCreate,
  RequireUpdate,
  RequireDelete,
  RequireManage,
  RequirePermissions,
  CheckPolicies,
  TenantIsolated,
  CrossTenant,
  PermissionCheckerService,
} from '../index';
import { JwtUser } from '../../types/jwt-user.type';
import { AppAbility } from '../../types/models/casl.model';

/**
 * 權限系統使用範例控制器
 * 展示各種權限檢查方式的使用方法
 */
@ApiTags('examples/permissions')
@ApiBearerAuth()
@Controller('examples/permissions')
@UseGuards(UnifiedPermissionGuard)
export class ExampleController {
  constructor(
    private readonly permissionChecker: PermissionCheckerService
  ) {}

  /**
   * 基本權限檢查 - 使用 @RequireRead 裝飾器
   */
  @Get('users')
  @RequireRead('User')
  @ApiOperation({ summary: '獲取用戶列表 - 需要讀取用戶權限' })
  async getUsers() {
    return { message: '用戶列表', users: [] };
  }

  /**
   * 建立權限檢查 - 使用 @RequireCreate 裝飾器
   */
  @Post('users')
  @RequireCreate('User')
  @ApiOperation({ summary: '建立用戶 - 需要建立用戶權限' })
  async createUser(@Body() userData: any) {
    return { message: '用戶已建立', user: userData };
  }

  /**
   * 更新權限檢查 - 使用條件限制
   */
  @Put('users/:id')
  @RequireUpdate('User', { id: '{{userId}}' })
  @ApiOperation({ summary: '更新用戶 - 只能更新自己的資料' })
  async updateUser(@Param('id') id: string, @Body() userData: any) {
    return { message: `用戶 ${id} 已更新`, user: userData };
  }

  /**
   * 刪除權限檢查 - 使用 @RequireDelete 裝飾器
   */
  @Delete('users/:id')
  @RequireDelete('User')
  @ApiOperation({ summary: '刪除用戶 - 需要刪除用戶權限' })
  async deleteUser(@Param('id') id: string) {
    return { message: `用戶 ${id} 已刪除` };
  }

  /**
   * 管理權限檢查 - 使用 @RequireManage 裝飾器
   */
  @Get('admin/users')
  @RequireManage('User')
  @ApiOperation({ summary: '管理用戶 - 需要管理用戶權限' })
  async manageUsers() {
    return { message: '用戶管理面板', users: [] };
  }

  /**
   * 多重權限檢查 - 使用 @RequirePermissions 裝飾器
   */
  @Get('users/:id/profile')
  @RequirePermissions([
    { action: 'read', subject: 'User' },
    { action: 'read', subject: 'UserProfile', conditions: { userId: '{{userId}}' } }
  ])
  @ApiOperation({ summary: '獲取用戶檔案 - 需要多重權限' })
  async getUserProfile(@Param('id') id: string) {
    return { message: `用戶 ${id} 的檔案`, profile: {} };
  }

  /**
   * 使用 @CheckPolicies 裝飾器進行複雜權限檢查
   */
  @Get('users/:id/sensitive-data')
  @CheckPolicies((ability: AppAbility) => {
    return ability.can('read', 'User') && ability.can('access', 'SensitiveData');
  })
  @ApiOperation({ summary: '獲取敏感資料 - 複雜權限檢查' })
  async getSensitiveData(@Param('id') id: string) {
    return { message: `用戶 ${id} 的敏感資料`, data: {} };
  }

  /**
   * 租戶隔離 - 使用 @TenantIsolated 裝飾器
   */
  @Get('tenant/:tenantId/users')
  @TenantIsolated()
  @RequireRead('User')
  @ApiOperation({ summary: '獲取租戶用戶 - 租戶隔離' })
  async getTenantUsers(@Param('tenantId') tenantId: string) {
    return { message: `租戶 ${tenantId} 的用戶列表`, users: [] };
  }

  /**
   * 跨租戶訪問 - 使用 @CrossTenant 裝飾器（僅系統管理員）
   */
  @Get('admin/all-tenants/users')
  @CrossTenant()
  @RequireManage('User')
  @ApiOperation({ summary: '獲取所有租戶用戶 - 跨租戶訪問' })
  async getAllTenantsUsers() {
    return { message: '所有租戶的用戶列表', users: [] };
  }

  /**
   * 程式化權限檢查範例
   */
  @Get('check-permissions')
  @ApiOperation({ summary: '程式化權限檢查範例' })
  async checkPermissions(@Req() req: Request) {
    const user = req.user as JwtUser;

    // 檢查單一權限
    const canReadUsers = await this.permissionChecker.canRead(user, 'User');
    
    // 批量檢查權限
    const permissionResults = await this.permissionChecker.checkPermissions(user, [
      { action: 'read', subject: 'User' },
      { action: 'create', subject: 'User' },
      { action: 'update', subject: 'User' },
      { action: 'delete', subject: 'User' },
    ]);

    // 檢查管理員身份
    const isSystemAdmin = this.permissionChecker.isSystemAdmin(user);
    const isTenantAdmin = this.permissionChecker.isTenantAdmin(user);

    return {
      user: {
        id: user.id,
        userType: user.userType,
        role: user.role,
        tenantId: user.tenantId,
      },
      permissions: {
        canReadUsers,
        batchCheck: permissionResults,
        isSystemAdmin,
        isTenantAdmin,
      },
    };
  }

  /**
   * 動態權限檢查範例
   */
  @Get('users/:id/dynamic-check')
  @ApiOperation({ summary: '動態權限檢查範例' })
  async dynamicPermissionCheck(
    @Param('id') userId: string,
    @Req() req: Request
  ) {
    const user = req.user as JwtUser;
    const ability = req.ability as AppAbility;

    // 模擬獲取目標用戶資料
    const targetUser = { id: userId, tenantId: 'tenant-1' };

    // 動態檢查是否可以訪問特定用戶的資料
    const canAccessUser = ability.can('read', 'User', { id: userId });
    const canAccessSameTenant = user.tenantId === targetUser.tenantId;

    if (!canAccessUser || (!canAccessSameTenant && user.userType === 'tenant')) {
      return { 
        message: '權限不足', 
        canAccess: false,
        reason: '無法訪問其他租戶的用戶資料'
      };
    }

    return {
      message: `可以訪問用戶 ${userId} 的資料`,
      canAccess: true,
      targetUser,
    };
  }

  /**
   * 無權限檢查的公開端點
   */
  @Get('public')
  @ApiOperation({ summary: '公開端點 - 無權限檢查' })
  async publicEndpoint() {
    return { message: '這是一個公開端點，不需要特殊權限' };
  }
}
