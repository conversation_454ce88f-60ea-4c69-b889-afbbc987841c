import { 
  Injectable, 
  CanActivate, 
  ExecutionContext, 
  Logger,
  UnauthorizedException,
  ForbiddenException 
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { CaslAbilityFactory } from "../casl-ability.factory";
import { AppAbility } from "../../types/models/casl.model";
import { CHECK_POLICIES_KEY } from "../decorators/check-policies.decorator";
import { REQUIRE_PERMISSIONS_KEY, PermissionRequirement } from "../decorators/require-permissions.decorator";
import { TENANT_CONTEXT_KEY, TenantContextConfig } from "../middleware/tenant-context.middleware";
import { PolicyHandler } from "../interfaces/policy-handler.interface";
import { JwtUser } from "../../types/jwt-user.type";
import { PermissionCheckerService } from "../services/permission-checker.service";

/**
 * 統一權限守衛
 * 整合所有權限檢查功能，包括：
 * - @CheckPolicies 裝飾器支援
 * - @RequirePermissions 裝飾器支援
 * - 租戶上下文驗證
 * - 詳細的日誌記錄和錯誤處理
 */
@Injectable()
export class UnifiedPermissionGuard implements CanActivate {
  private readonly logger = new Logger(UnifiedPermissionGuard.name);

  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
    private permissionChecker: PermissionCheckerService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const handlerName = context.getHandler().name;
    const className = context.getClass().name;

    try {
      // 獲取所有權限相關的元數據
      const metadata = this.extractPermissionMetadata(context);
      
      // 如果沒有定義任何權限檢查，允許訪問
      if (!this.hasPermissionChecks(metadata)) {
        this.logger.debug(
          `No permission checks defined for ${className}.${handlerName}, allowing access`
        );
        return true;
      }

      // 驗證用戶是否存在
      if (!user || !user.id) {
        this.logger.warn(
          `Unauthorized access attempt to ${className}.${handlerName}: No user or user ID`
        );
        throw new UnauthorizedException('用戶未認證或用戶資訊不完整');
      }

      this.logger.debug(
        `Checking permissions for user ${user.id} (${user.userType}) accessing ${className}.${handlerName}`
      );

      // 建立用戶能力實例
      const ability = await this.createUserAbility(user);
      request.ability = ability;

      // 驗證租戶上下文
      await this.validateTenantContext(user, request, metadata.tenantContext);

      // 執行權限檢查
      const permissionResults = await this.executePermissionChecks(
        metadata, 
        ability, 
        user, 
        request, 
        context
      );

      const allPassed = permissionResults.every(result => result === true);
      const executionTime = Date.now() - startTime;

      if (allPassed) {
        this.logger.debug(
          `Permission check passed for ${className}.${handlerName} ` +
          `(user: ${user.id}, execution time: ${executionTime}ms)`
        );
        return true;
      } else {
        this.logger.warn(
          `Permission denied for user ${user.id} (${user.userType}) ` +
          `accessing ${className}.${handlerName}. ` +
          `Results: [${permissionResults.join(', ')}]`
        );
        throw new ForbiddenException('權限不足，無法執行此操作');
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(
        `Unexpected error in permission check for ${className}.${handlerName} ` +
        `(user: ${user?.id || 'unknown'}, execution time: ${executionTime}ms):`,
        error.stack
      );
      
      throw new ForbiddenException('權限檢查過程中發生錯誤');
    }
  }

  /**
   * 提取權限相關的元數據
   */
  private extractPermissionMetadata(context: ExecutionContext) {
    return {
      policyHandlers: this.reflector.getAllAndOverride<PolicyHandler[]>(
        CHECK_POLICIES_KEY,
        [context.getHandler(), context.getClass()]
      ),
      permissionRequirements: this.reflector.getAllAndOverride<PermissionRequirement[]>(
        REQUIRE_PERMISSIONS_KEY,
        [context.getHandler(), context.getClass()]
      ),
      tenantContext: this.reflector.getAllAndOverride<TenantContextConfig>(
        TENANT_CONTEXT_KEY,
        [context.getHandler(), context.getClass()]
      ),
    };
  }

  /**
   * 檢查是否有權限檢查定義
   */
  private hasPermissionChecks(metadata: any): boolean {
    return (metadata.policyHandlers && metadata.policyHandlers.length > 0) ||
           (metadata.permissionRequirements && metadata.permissionRequirements.length > 0);
  }

  /**
   * 建立用戶能力實例
   */
  private async createUserAbility(user: JwtUser): Promise<AppAbility> {
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        id: user.id,
        tenantId: user.tenantId,
      });

      this.logger.debug(
        `Created ability for user ${user.id} with ${ability.rules.length} rules`
      );

      return ability;
    } catch (error) {
      this.logger.error(
        `Failed to create ability for user ${user.id}:`,
        error.message
      );
      throw new ForbiddenException('無法建立用戶權限實例');
    }
  }

  /**
   * 驗證租戶上下文
   */
  private async validateTenantContext(
    user: JwtUser, 
    request: any, 
    tenantConfig?: TenantContextConfig
  ): Promise<void> {
    // 如果沒有租戶配置，使用預設驗證
    if (!tenantConfig) {
      return this.defaultTenantValidation(user, request);
    }

    // 如果有自訂驗證器，使用它
    if (tenantConfig.validator) {
      const isValid = await tenantConfig.validator(request, user);
      if (!isValid) {
        throw new ForbiddenException('租戶上下文驗證失敗');
      }
      return;
    }

    // 使用配置進行驗證
    if (tenantConfig.required && user.userType === 'tenant' && !user.tenantId) {
      throw new ForbiddenException('租戶用戶缺少租戶資訊');
    }

    // 檢查指定來源的租戶 ID
    if (tenantConfig.sources) {
      for (const source of tenantConfig.sources) {
        const tenantId = this.extractTenantIdFromSource(request, source);
        if (tenantId && user.tenantId && tenantId !== user.tenantId) {
          throw new ForbiddenException('無權訪問其他租戶的資源');
        }
      }
    }
  }

  /**
   * 預設租戶驗證
   */
  private defaultTenantValidation(user: JwtUser, request: any): void {
    if (user.userType === 'system') {
      return; // 系統用戶跳過驗證
    }

    if (user.userType === 'tenant' && !user.tenantId) {
      throw new ForbiddenException('租戶用戶缺少租戶資訊');
    }

    const routeTenantId = request.params?.tenantId || request.query?.tenantId;
    if (routeTenantId && user.tenantId && routeTenantId !== user.tenantId) {
      throw new ForbiddenException('無權訪問其他租戶的資源');
    }
  }

  /**
   * 從指定來源提取租戶 ID
   */
  private extractTenantIdFromSource(request: any, source: string): string | undefined {
    switch (source) {
      case 'params':
        return request.params?.tenantId;
      case 'query':
        return request.query?.tenantId;
      case 'body':
        return request.body?.tenantId;
      case 'header':
        return request.headers?.['x-tenant-id'];
      default:
        return undefined;
    }
  }

  /**
   * 執行權限檢查
   */
  private async executePermissionChecks(
    metadata: any,
    ability: AppAbility,
    user: JwtUser,
    request: any,
    context: ExecutionContext
  ): Promise<boolean[]> {
    const checks: Promise<boolean>[] = [];

    // 執行 @CheckPolicies 檢查
    if (metadata.policyHandlers) {
      checks.push(
        ...metadata.policyHandlers.map((handler: PolicyHandler) =>
          this.execPolicyHandler(handler, ability, context)
        )
      );
    }

    // 執行 @RequirePermissions 檢查
    if (metadata.permissionRequirements) {
      checks.push(
        ...metadata.permissionRequirements.map((req: PermissionRequirement) =>
          this.checkPermissionRequirement(req, user)
        )
      );
    }

    return Promise.all(checks);
  }

  /**
   * 執行權限策略處理器
   */
  private async execPolicyHandler(
    handler: PolicyHandler,
    ability: AppAbility,
    context: ExecutionContext
  ): Promise<boolean> {
    try {
      if (typeof handler === "function") {
        return handler(ability);
      } else if (handler && typeof handler.handle === "function") {
        return handler.handle(ability);
      } else {
        this.logger.error('Invalid policy handler type:', typeof handler);
        return false;
      }
    } catch (error) {
      this.logger.error('Policy handler execution error:', error.message);
      return false;
    }
  }

  /**
   * 檢查權限要求
   */
  private async checkPermissionRequirement(
    requirement: PermissionRequirement,
    user: JwtUser
  ): Promise<boolean> {
    try {
      const result = await this.permissionChecker.checkPermission(
        user,
        requirement.action,
        requirement.subject,
        requirement.conditions,
        requirement.fields
      );
      return result.granted;
    } catch (error) {
      this.logger.error(
        `Error checking permission requirement ${requirement.action}:${requirement.subject}:`,
        error.message
      );
      return false;
    }
  }
}
