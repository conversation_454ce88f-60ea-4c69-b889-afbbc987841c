import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  UnauthorizedException,
  ForbiddenException
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { CaslAbilityFactory } from "../casl-ability.factory";
import { AppAbility } from "../../types/models/casl.model";
import { CHECK_POLICIES_KEY } from "../decorators/check-policies.decorator";
import { PolicyHandler } from "../interfaces/policy-handler.interface";
import { JwtUser } from "../../types/jwt-user.type";

// Extend Express Request object to include 'ability' for CASL
declare global {
  namespace Express {
    interface Request {
      ability?: AppAbility;
      user?: JwtUser;
    }
  }
}

/**
 * 增強的權限守衛
 * 提供強健的 CASL 權限檢查，包含詳細的日誌記錄和錯誤處理
 */
@Injectable()
export class PoliciesGuard implements CanActivate {
  private readonly logger = new Logger(PoliciesGuard.name);

  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const handlerName = context.getHandler().name;
    const className = context.getClass().name;

    try {
      // 獲取權限策略處理器
      const policyHandlers = this.reflector.getAllAndOverride<PolicyHandler[]>(
        CHECK_POLICIES_KEY,
        [context.getHandler(), context.getClass()]
      );

      // 如果沒有定義權限策略，允許訪問（可根據需求調整為拒絕）
      if (!policyHandlers || policyHandlers.length === 0) {
        this.logger.debug(
          `No policies defined for ${className}.${handlerName}, allowing access`
        );
        return true;
      }

      // 驗證用戶是否存在
      if (!user || !user.id) {
        this.logger.warn(
          `Unauthorized access attempt to ${className}.${handlerName}: No user or user ID`
        );
        throw new UnauthorizedException('用戶未認證或用戶資訊不完整');
      }

      this.logger.debug(
        `Checking permissions for user ${user.id} (${user.userType}) accessing ${className}.${handlerName}`
      );

      // 建立用戶能力實例
      const ability = await this.createUserAbility(user);

      // 將能力實例附加到請求對象，供控制器使用
      request.ability = ability;

      // 執行所有權限策略檢查
      const permissionResults = await Promise.all(
        policyHandlers.map(async (handler, index) => {
          try {
            const result = await this.execPolicyHandler(handler, ability, context);
            this.logger.debug(
              `Policy handler ${index + 1}/${policyHandlers.length} result: ${result}`
            );
            return result;
          } catch (error) {
            this.logger.error(
              `Policy handler ${index + 1} execution failed:`,
              error.message
            );
            return false;
          }
        })
      );

      const allPassed = permissionResults.every(result => result === true);
      const executionTime = Date.now() - startTime;

      if (allPassed) {
        this.logger.debug(
          `Permission check passed for ${className}.${handlerName} ` +
          `(user: ${user.id}, execution time: ${executionTime}ms)`
        );
        return true;
      } else {
        this.logger.warn(
          `Permission denied for user ${user.id} (${user.userType}) ` +
          `accessing ${className}.${handlerName}. ` +
          `Results: [${permissionResults.join(', ')}]`
        );
        throw new ForbiddenException('權限不足，無法執行此操作');
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;

      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        // 重新拋出已知的權限錯誤
        throw error;
      }

      // 記錄未預期的錯誤
      this.logger.error(
        `Unexpected error in permission check for ${className}.${handlerName} ` +
        `(user: ${user?.id || 'unknown'}, execution time: ${executionTime}ms):`,
        error.stack
      );

      // 對於未預期的錯誤，出於安全考慮拒絕訪問
      throw new ForbiddenException('權限檢查過程中發生錯誤');
    }
  }

  /**
   * 建立用戶能力實例，包含錯誤處理和快取
   */
  private async createUserAbility(user: JwtUser): Promise<AppAbility> {
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        id: user.id,
        tenantId: user.tenantId,
      });

      this.logger.debug(
        `Created ability for user ${user.id} with ${ability.rules.length} rules`
      );

      return ability;
    } catch (error) {
      this.logger.error(
        `Failed to create ability for user ${user.id}:`,
        error.message
      );
      throw new ForbiddenException('無法建立用戶權限實例');
    }
  }

  /**
   * 執行權限策略處理器
   */
  private async execPolicyHandler(
    handler: PolicyHandler,
    ability: AppAbility,
    context: ExecutionContext
  ): Promise<boolean> {
    try {
      if (typeof handler === "function") {
        // 函數型處理器
        return handler(ability);
      } else if (handler && typeof handler.handle === "function") {
        // 對象型處理器
        return handler.handle(ability);
      } else {
        this.logger.error('Invalid policy handler type:', typeof handler);
        return false;
      }
    } catch (error) {
      this.logger.error('Policy handler execution error:', error.message);
      return false;
    }
  }
}
