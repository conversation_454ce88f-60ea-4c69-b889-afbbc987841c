import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UnifiedPermissionGuard } from './unified-permission.guard';
import { CaslAbilityFactory } from '../casl-ability.factory';
import { PermissionCheckerService } from '../services/permission-checker.service';
import { JwtUser } from '../../types/jwt-user.type';
import { AppAbility } from '../../types/models/casl.model';

describe('UnifiedPermissionGuard', () => {
  let guard: UnifiedPermissionGuard;
  let reflector: Reflector;
  let caslAbilityFactory: CaslAbilityFactory;
  let permissionChecker: PermissionCheckerService;

  const mockUser: JwtUser = {
    id: 'user-1',
    email: '<EMAIL>',
    userType: 'tenant',
    tenantId: 'tenant-1',
    role: 'TENANT_USER',
    iat: Date.now(),
    exp: Date.now() + 3600000,
  };

  const mockAbility = {
    can: jest.fn(),
    rules: [],
  } as unknown as AppAbility;

  const mockExecutionContext = {
    switchToHttp: () => ({
      getRequest: () => ({
        user: mockUser,
        params: {},
        query: {},
        body: {},
      }),
    }),
    getHandler: () => ({ name: 'testHandler' }),
    getClass: () => ({ name: 'TestController' }),
  } as ExecutionContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UnifiedPermissionGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: CaslAbilityFactory,
          useValue: {
            createForUser: jest.fn().mockResolvedValue(mockAbility),
          },
        },
        {
          provide: PermissionCheckerService,
          useValue: {
            checkPermission: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<UnifiedPermissionGuard>(UnifiedPermissionGuard);
    reflector = module.get<Reflector>(Reflector);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
    permissionChecker = module.get<PermissionCheckerService>(PermissionCheckerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('canActivate', () => {
    it('should allow access when no permission checks are defined', async () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(undefined);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it('should throw UnauthorizedException when user is not present', async () => {
      const contextWithoutUser = {
        ...mockExecutionContext,
        switchToHttp: () => ({
          getRequest: () => ({ user: null }),
        }),
      } as ExecutionContext;

      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        { action: 'read', subject: 'User' }
      ]);

      await expect(guard.canActivate(contextWithoutUser)).rejects.toThrow(
        UnauthorizedException
      );
    });

    it('should allow access when all permission checks pass', async () => {
      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([]) // policyHandlers
        .mockReturnValueOnce([{ action: 'read', subject: 'User' }]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      jest.spyOn(permissionChecker, 'checkPermission').mockResolvedValue({
        granted: true,
        permission: { action: 'read', subject: 'User' },
        user: { id: mockUser.id, userType: mockUser.userType, tenantId: mockUser.tenantId },
        timestamp: new Date(),
      });

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(caslAbilityFactory.createForUser).toHaveBeenCalledWith({
        id: mockUser.id,
        tenantId: mockUser.tenantId,
      });
    });

    it('should deny access when permission check fails', async () => {
      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([]) // policyHandlers
        .mockReturnValueOnce([{ action: 'delete', subject: 'User' }]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      jest.spyOn(permissionChecker, 'checkPermission').mockResolvedValue({
        granted: false,
        permission: { action: 'delete', subject: 'User' },
        user: { id: mockUser.id, userType: mockUser.userType, tenantId: mockUser.tenantId },
        reason: '權限不足',
        timestamp: new Date(),
      });

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should validate tenant context for tenant users', async () => {
      const contextWithTenantMismatch = {
        ...mockExecutionContext,
        switchToHttp: () => ({
          getRequest: () => ({
            user: mockUser,
            params: { tenantId: 'different-tenant' },
            query: {},
            body: {},
          }),
        }),
      } as ExecutionContext;

      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([]) // policyHandlers
        .mockReturnValueOnce([{ action: 'read', subject: 'User' }]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      await expect(guard.canActivate(contextWithTenantMismatch)).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should allow system users to access any tenant resources', async () => {
      const systemUser: JwtUser = {
        ...mockUser,
        userType: 'system',
        role: 'SYSTEM_ADMIN',
        tenantId: undefined,
      };

      const contextWithSystemUser = {
        ...mockExecutionContext,
        switchToHttp: () => ({
          getRequest: () => ({
            user: systemUser,
            params: { tenantId: 'any-tenant' },
            query: {},
            body: {},
          }),
        }),
      } as ExecutionContext;

      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([]) // policyHandlers
        .mockReturnValueOnce([{ action: 'read', subject: 'User' }]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      jest.spyOn(permissionChecker, 'checkPermission').mockResolvedValue({
        granted: true,
        permission: { action: 'read', subject: 'User' },
        user: { id: systemUser.id, userType: systemUser.userType },
        timestamp: new Date(),
      });

      const result = await guard.canActivate(contextWithSystemUser);

      expect(result).toBe(true);
    });

    it('should handle policy handlers correctly', async () => {
      const mockPolicyHandler = jest.fn().mockReturnValue(true);

      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([mockPolicyHandler]) // policyHandlers
        .mockReturnValueOnce([]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockPolicyHandler).toHaveBeenCalledWith(mockAbility);
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(reflector, 'getAllAndOverride')
        .mockReturnValueOnce([]) // policyHandlers
        .mockReturnValueOnce([{ action: 'read', subject: 'User' }]) // permissionRequirements
        .mockReturnValueOnce(undefined); // tenantContext

      jest.spyOn(caslAbilityFactory, 'createForUser').mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        ForbiddenException
      );
    });
  });
});
