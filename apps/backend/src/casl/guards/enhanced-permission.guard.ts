import { 
  Injectable, 
  CanActivate, 
  ExecutionContext, 
  Logger,
  UnauthorizedException,
  ForbiddenException 
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { CaslAbilityFactory } from "../casl-ability.factory";
import { AppAbility } from "../../types/models/casl.model";
import { CHECK_POLICIES_KEY } from "../decorators/check-policies.decorator";
import { REQUIRE_PERMISSIONS_KEY, PermissionRequirement } from "../decorators/require-permissions.decorator";
import { PolicyHandler } from "../interfaces/policy-handler.interface";
import { JwtUser } from "../../types/jwt-user.type";

/**
 * 增強的權限守衛
 * 支援 @CheckPolicies 和 @RequirePermissions 裝飾器
 * 提供租戶上下文驗證和詳細的權限檢查
 */
@Injectable()
export class EnhancedPermissionGuard implements CanActivate {
  private readonly logger = new Logger(EnhancedPermissionGuard.name);

  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const handlerName = context.getHandler().name;
    const className = context.getClass().name;

    try {
      // 獲取權限策略處理器和權限要求
      const policyHandlers = this.reflector.getAllAndOverride<PolicyHandler[]>(
        CHECK_POLICIES_KEY,
        [context.getHandler(), context.getClass()]
      );

      const permissionRequirements = this.reflector.getAllAndOverride<PermissionRequirement[]>(
        REQUIRE_PERMISSIONS_KEY,
        [context.getHandler(), context.getClass()]
      );

      // 如果沒有定義任何權限檢查，允許訪問
      if ((!policyHandlers || policyHandlers.length === 0) && 
          (!permissionRequirements || permissionRequirements.length === 0)) {
        this.logger.debug(
          `No permission checks defined for ${className}.${handlerName}, allowing access`
        );
        return true;
      }

      // 驗證用戶是否存在
      if (!user || !user.id) {
        this.logger.warn(
          `Unauthorized access attempt to ${className}.${handlerName}: No user or user ID`
        );
        throw new UnauthorizedException('用戶未認證或用戶資訊不完整');
      }

      this.logger.debug(
        `Checking permissions for user ${user.id} (${user.userType}) accessing ${className}.${handlerName}`
      );

      // 建立用戶能力實例
      const ability = await this.createUserAbility(user);
      
      // 將能力實例附加到請求對象
      request.ability = ability;

      // 驗證租戶上下文（如果需要）
      await this.validateTenantContext(user, request, context);

      // 執行權限檢查
      const permissionChecks: Promise<boolean>[] = [];

      // 檢查 @CheckPolicies 裝飾器
      if (policyHandlers && policyHandlers.length > 0) {
        permissionChecks.push(
          ...policyHandlers.map(handler => this.execPolicyHandler(handler, ability, context))
        );
      }

      // 檢查 @RequirePermissions 裝飾器
      if (permissionRequirements && permissionRequirements.length > 0) {
        permissionChecks.push(
          ...permissionRequirements.map(req => this.checkPermissionRequirement(req, ability, user, request))
        );
      }

      const results = await Promise.all(permissionChecks);
      const allPassed = results.every(result => result === true);
      const executionTime = Date.now() - startTime;

      if (allPassed) {
        this.logger.debug(
          `Permission check passed for ${className}.${handlerName} ` +
          `(user: ${user.id}, execution time: ${executionTime}ms)`
        );
        return true;
      } else {
        this.logger.warn(
          `Permission denied for user ${user.id} (${user.userType}) ` +
          `accessing ${className}.${handlerName}. ` +
          `Results: [${results.join(', ')}]`
        );
        throw new ForbiddenException('權限不足，無法執行此操作');
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(
        `Unexpected error in permission check for ${className}.${handlerName} ` +
        `(user: ${user?.id || 'unknown'}, execution time: ${executionTime}ms):`,
        error.stack
      );
      
      throw new ForbiddenException('權限檢查過程中發生錯誤');
    }
  }

  /**
   * 建立用戶能力實例
   */
  private async createUserAbility(user: JwtUser): Promise<AppAbility> {
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        id: user.id,
        tenantId: user.tenantId,
      });

      this.logger.debug(
        `Created ability for user ${user.id} with ${ability.rules.length} rules`
      );

      return ability;
    } catch (error) {
      this.logger.error(
        `Failed to create ability for user ${user.id}:`,
        error.message
      );
      throw new ForbiddenException('無法建立用戶權限實例');
    }
  }

  /**
   * 驗證租戶上下文
   */
  private async validateTenantContext(
    user: JwtUser, 
    request: any, 
    context: ExecutionContext
  ): Promise<void> {
    // 如果是系統用戶，跳過租戶驗證
    if (user.userType === 'system') {
      return;
    }

    // 對於租戶用戶，確保有租戶 ID
    if (user.userType === 'tenant' && !user.tenantId) {
      this.logger.warn(
        `Tenant user ${user.id} missing tenantId in JWT`
      );
      throw new ForbiddenException('租戶用戶缺少租戶資訊');
    }

    // 檢查路由參數中的租戶 ID 是否與用戶的租戶 ID 匹配
    const routeTenantId = request.params?.tenantId;
    if (routeTenantId && user.tenantId && routeTenantId !== user.tenantId) {
      this.logger.warn(
        `Tenant mismatch: user ${user.id} (tenant: ${user.tenantId}) ` +
        `trying to access tenant ${routeTenantId}`
      );
      throw new ForbiddenException('無權訪問其他租戶的資源');
    }
  }

  /**
   * 執行權限策略處理器
   */
  private async execPolicyHandler(
    handler: PolicyHandler,
    ability: AppAbility,
    context: ExecutionContext
  ): Promise<boolean> {
    try {
      if (typeof handler === "function") {
        return handler(ability);
      } else if (handler && typeof handler.handle === "function") {
        return handler.handle(ability);
      } else {
        this.logger.error('Invalid policy handler type:', typeof handler);
        return false;
      }
    } catch (error) {
      this.logger.error('Policy handler execution error:', error.message);
      return false;
    }
  }

  /**
   * 檢查權限要求
   */
  private async checkPermissionRequirement(
    requirement: PermissionRequirement,
    ability: AppAbility,
    user: JwtUser,
    request: any
  ): Promise<boolean> {
    try {
      const { action, subject, conditions, fields } = requirement;
      
      // 處理條件中的變數替換
      const processedConditions = this.processConditions(conditions, user, request);
      
      // 執行權限檢查
      const hasPermission = fields && fields.length > 0
        ? ability.can(action, subject, fields[0]) // CASL 的 can 方法只接受單一欄位
        : ability.can(action, subject, processedConditions);

      this.logger.debug(
        `Permission check: ${action}:${subject} for user ${user.id} = ${hasPermission}`
      );

      return hasPermission;
    } catch (error) {
      this.logger.error(
        `Error checking permission requirement ${requirement.action}:${requirement.subject}:`,
        error.message
      );
      return false;
    }
  }

  /**
   * 處理條件中的變數替換
   */
  private processConditions(
    conditions: Record<string, any> | undefined,
    user: JwtUser,
    request: any
  ): Record<string, any> | undefined {
    if (!conditions) return undefined;

    const processed = { ...conditions };
    
    // 替換常見的變數
    const replacements = {
      '{{userId}}': user.id,
      '{{tenantId}}': user.tenantId,
      '{{userType}}': user.userType,
    };

    // 遞歸處理條件對象
    const replaceVariables = (obj: any): any => {
      if (typeof obj === 'string') {
        let result = obj;
        for (const [variable, value] of Object.entries(replacements)) {
          if (value !== undefined) {
            result = result.replace(variable, value);
          }
        }
        return result;
      } else if (Array.isArray(obj)) {
        return obj.map(replaceVariables);
      } else if (obj && typeof obj === 'object') {
        const newObj: any = {};
        for (const [key, value] of Object.entries(obj)) {
          newObj[key] = replaceVariables(value);
        }
        return newObj;
      }
      return obj;
    };

    return replaceVariables(processed);
  }
}
