import { Injectable, Logger } from '@nestjs/common';
import { RoleAssignmentService } from '../services/role-assignment.service';
import { RoleHierarchyService } from '../services/role-hierarchy.service';
import { UserRoleService } from '../services/user-role.service';
import { UserType } from '../interfaces/role-assignment.interface';
import { RoleScope } from '@prisma/client';

/**
 * 角色管理使用範例
 * 展示如何使用角色指派和管理服務
 */
@Injectable()
export class RoleManagementExample {
  private readonly logger = new Logger(RoleManagementExample.name);

  constructor(
    private readonly roleAssignmentService: RoleAssignmentService,
    private readonly roleHierarchyService: RoleHierarchyService,
    private readonly userRoleService: UserRoleService
  ) {}

  /**
   * 範例 1: 新員工入職流程
   */
  async onboardNewEmployee(
    userId: string,
    userType: UserType,
    position: 'admin' | 'manager' | 'user',
    tenantId?: string
  ) {
    this.logger.log(`Starting onboarding process for ${userId} as ${position}`);

    try {
      // 1. 根據職位確定角色
      const roleIds = this.getRolesByPosition(position, userType);

      // 2. 指派角色
      const assignmentResult = await this.roleAssignmentService.assignRoles({
        userId,
        userType,
        roleIds,
        tenantId,
        reason: `新員工入職 - ${position}`,
        assignedBy: 'system',
      });

      if (!assignmentResult.success) {
        throw new Error(`角色指派失敗: ${assignmentResult.error}`);
      }

      // 3. 驗證權限
      const effectivePermissions = await this.userRoleService.getUserEffectivePermissions(
        userId,
        userType
      );

      // 4. 同步權限快取
      await this.userRoleService.syncUserPermissionCache(userId, userType);

      this.logger.log(
        `Successfully onboarded ${userId} with ${roleIds.length} roles and ${effectivePermissions.length} permissions`
      );

      return {
        success: true,
        assignedRoles: assignmentResult.assignedRoles,
        effectivePermissions,
      };
    } catch (error) {
      this.logger.error(`Onboarding failed for ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * 範例 2: 員工升職流程
   */
  async promoteEmployee(
    userId: string,
    userType: UserType,
    fromPosition: string,
    toPosition: string,
    tenantId?: string
  ) {
    this.logger.log(`Promoting ${userId} from ${fromPosition} to ${toPosition}`);

    try {
      // 1. 獲取當前角色
      const currentRoleInfo = await this.roleAssignmentService.getUserRoleInfo(userId, userType);
      const currentRoleIds = currentRoleInfo.roles.map((r) => r.id);

      // 2. 確定新角色
      const newRoleIds = this.getRolesByPosition(toPosition as any, userType);

      // 3. 替換角色
      const replaceResult = await this.roleAssignmentService.replaceRoles({
        userId,
        userType,
        roleIds: newRoleIds,
        tenantId,
        reason: `升職: ${fromPosition} -> ${toPosition}`,
        assignedBy: 'hr-system',
      });

      if (!replaceResult.success) {
        throw new Error(`角色替換失敗: ${replaceResult.error}`);
      }

      // 4. 記錄變更
      this.logger.log(
        `Promotion completed: removed [${currentRoleIds.join(', ')}], assigned [${newRoleIds.join(', ')}]`
      );

      return {
        success: true,
        previousRoles: currentRoleIds,
        newRoles: newRoleIds,
        effectivePermissions: await this.userRoleService.getUserEffectivePermissions(userId, userType),
      };
    } catch (error) {
      this.logger.error(`Promotion failed for ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * 範例 3: 組織架構調整
   */
  async reorganizeTeam(
    teamMembers: Array<{
      userId: string;
      userType: UserType;
      currentPosition: string;
      newPosition: string;
      tenantId?: string;
    }>
  ) {
    this.logger.log(`Starting team reorganization for ${teamMembers.length} members`);

    try {
      // 1. 準備批量操作
      const operations = teamMembers.map((member) => ({
        userId: member.userId,
        userType: member.userType,
        action: 'replace' as const,
        roleIds: this.getRolesByPosition(member.newPosition as any, member.userType),
        tenantId: member.tenantId,
      }));

      // 2. 執行批量操作
      const batchResult = await this.roleAssignmentService.batchRoleOperations({
        operations,
        reason: '組織架構調整',
        operatedBy: 'management',
      });

      // 3. 批量同步權限快取
      const syncResult = await this.userRoleService.batchSyncUserPermissionCache(
        teamMembers.map((member) => ({
          userId: member.userId,
          userType: member.userType,
        }))
      );

      this.logger.log(
        `Team reorganization completed: ${batchResult.successCount} success, ${batchResult.failureCount} failures`
      );

      return {
        batchResult,
        syncResult,
      };
    } catch (error) {
      this.logger.error('Team reorganization failed:', error.message);
      throw error;
    }
  }

  /**
   * 範例 4: 權限審計
   */
  async auditUserPermissions(userId: string, userType: UserType) {
    this.logger.log(`Starting permission audit for user ${userId}`);

    try {
      // 1. 獲取用戶角色層級資訊
      const roleHierarchy = await this.userRoleService.getUserRoleHierarchy(userId, userType);

      // 2. 分析權限來源
      const permissionAnalysis = {
        totalRoles: roleHierarchy.roleHierarchies.length,
        totalDirectPermissions: 0,
        totalInheritedPermissions: 0,
        totalEffectivePermissions: 0,
        roleDetails: [] as any[],
      };

      for (const role of roleHierarchy.roleHierarchies) {
        permissionAnalysis.totalDirectPermissions += role.directPermissions.length;
        permissionAnalysis.totalInheritedPermissions += role.inheritedPermissions.length;
        permissionAnalysis.totalEffectivePermissions += role.effectivePermissions.length;

        permissionAnalysis.roleDetails.push({
          roleId: role.roleId,
          roleName: role.roleName,
          level: role.level,
          directPermissions: role.directPermissions.length,
          inheritedPermissions: role.inheritedPermissions.length,
          effectivePermissions: role.effectivePermissions.length,
          parentRoles: role.parentRoles,
          childRoles: role.childRoles,
        });
      }

      // 3. 檢查關鍵權限
      const criticalPermissions = [
        'users:manage',
        'roles:manage',
        'tenants:manage',
        'system:admin',
      ];

      const criticalPermissionCheck = await Promise.all(
        criticalPermissions.map(async (permission) => ({
          permission,
          hasPermission: await this.userRoleService.userHasPermission(userId, userType, permission),
        }))
      );

      this.logger.log(`Permission audit completed for user ${userId}`);

      return {
        user: roleHierarchy.user,
        permissionAnalysis,
        criticalPermissions: criticalPermissionCheck,
        auditTimestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Permission audit failed for user ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * 範例 5: 角色層級驗證和修復
   */
  async validateAndFixRoleHierarchy() {
    this.logger.log('Starting role hierarchy validation');

    try {
      // 1. 驗證層級結構
      const validationResult = await this.roleHierarchyService.validateHierarchy();

      if (validationResult.isValid) {
        this.logger.log('Role hierarchy is valid');
        return {
          isValid: true,
          errors: [],
          warnings: validationResult.warnings,
        };
      }

      // 2. 記錄問題
      this.logger.warn('Role hierarchy validation failed:', {
        errors: validationResult.errors,
        warnings: validationResult.warnings,
      });

      // 3. 這裡可以實作自動修復邏輯
      // 例如：移除循環引用、修復範圍衝突等

      return {
        isValid: false,
        errors: validationResult.errors,
        warnings: validationResult.warnings,
        suggestedFixes: this.generateFixSuggestions(validationResult),
      };
    } catch (error) {
      this.logger.error('Role hierarchy validation failed:', error.message);
      throw error;
    }
  }

  /**
   * 範例 6: 權限報告生成
   */
  async generatePermissionReport(scope: RoleScope, tenantId?: string) {
    this.logger.log(`Generating permission report for scope: ${scope}`);

    try {
      // 1. 獲取範圍內的所有用戶
      const users = await this.userRoleService.getUsersByScope(scope, tenantId);

      // 2. 生成報告
      const report = {
        scope,
        tenantId,
        totalUsers: users.length,
        userSummary: [] as any[],
        roleDistribution: {} as Record<string, number>,
        permissionDistribution: {} as Record<string, number>,
        generatedAt: new Date(),
      };

      for (const user of users) {
        // 統計角色分布
        for (const role of user.roles) {
          report.roleDistribution[role.name] = (report.roleDistribution[role.name] || 0) + 1;
        }

        // 獲取有效權限
        const effectivePermissions = await this.userRoleService.getUserEffectivePermissions(
          user.userId,
          user.userType
        );

        // 統計權限分布
        for (const permission of effectivePermissions) {
          report.permissionDistribution[permission] = (report.permissionDistribution[permission] || 0) + 1;
        }

        report.userSummary.push({
          userId: user.userId,
          userType: user.userType,
          email: user.user.email,
          roleCount: user.roles.length,
          permissionCount: effectivePermissions.length,
          roles: user.roles.map((r) => r.name),
        });
      }

      this.logger.log(`Permission report generated for ${users.length} users`);
      return report;
    } catch (error) {
      this.logger.error('Permission report generation failed:', error.message);
      throw error;
    }
  }

  // ==================== 私有輔助方法 ====================

  private getRolesByPosition(position: 'admin' | 'manager' | 'user', userType: UserType): string[] {
    if (userType === UserType.SYSTEM) {
      switch (position) {
        case 'admin':
          return ['SYSTEM_ADMIN'];
        case 'manager':
          return ['SYSTEM_MANAGER'];
        case 'user':
          return ['SYSTEM_USER'];
        default:
          return ['SYSTEM_USER'];
      }
    } else {
      switch (position) {
        case 'admin':
          return ['TENANT_ADMIN'];
        case 'manager':
          return ['TENANT_MANAGER'];
        case 'user':
          return ['TENANT_USER'];
        default:
          return ['TENANT_USER'];
      }
    }
  }

  private generateFixSuggestions(validationResult: any): string[] {
    const suggestions: string[] = [];

    if (validationResult.errors.some((e: string) => e.includes('循環引用'))) {
      suggestions.push('移除檢測到的循環引用關係');
    }

    if (validationResult.errors.some((e: string) => e.includes('範圍衝突'))) {
      suggestions.push('修正角色範圍不兼容的繼承關係');
    }

    if (validationResult.warnings.some((w: string) => w.includes('孤立角色'))) {
      suggestions.push('考慮為孤立角色設置適當的層級關係');
    }

    return suggestions;
  }
}
