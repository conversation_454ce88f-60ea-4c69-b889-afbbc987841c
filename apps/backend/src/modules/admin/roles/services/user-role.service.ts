import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { RoleAssignmentService } from './role-assignment.service';
import { RoleHierarchyService } from './role-hierarchy.service';
import {
  UserType,
  UserRoleInfo,
  RoleChangeHistory,
} from '../interfaces/role-assignment.interface';
import { RoleScope } from '@prisma/client';

/**
 * 用戶角色管理服務
 * 提供高級的用戶角色管理功能
 */
@Injectable()
export class UserRoleService {
  private readonly logger = new Logger(UserRoleService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly roleAssignmentService: RoleAssignmentService,
    private readonly roleHierarchyService: RoleHierarchyService
  ) {}

  /**
   * 獲取用戶的有效權限（包含繼承權限）
   */
  async getUserEffectivePermissions(userId: string, userType: UserType): Promise<string[]> {
    this.logger.debug(`Getting effective permissions for user ${userId} (${userType})`);

    // 獲取用戶角色
    const userRoleInfo = await this.roleAssignmentService.getUserRoleInfo(userId, userType);
    const roleIds = userRoleInfo.roles.map((r) => r.id);

    if (roleIds.length === 0) {
      return [];
    }

    // 獲取所有角色的有效權限
    const allPermissions: string[] = [];
    for (const roleId of roleIds) {
      const effectivePermissions = await this.roleHierarchyService.getEffectivePermissions(roleId);
      allPermissions.push(...effectivePermissions);
    }

    // 去重並返回
    const uniquePermissions = [...new Set(allPermissions)];
    
    this.logger.debug(
      `User ${userId} has ${uniquePermissions.length} effective permissions from ${roleIds.length} roles`
    );

    return uniquePermissions;
  }

  /**
   * 檢查用戶是否有特定權限
   */
  async userHasPermission(
    userId: string,
    userType: UserType,
    permissionId: string
  ): Promise<boolean> {
    const effectivePermissions = await this.getUserEffectivePermissions(userId, userType);
    return effectivePermissions.includes(permissionId);
  }

  /**
   * 檢查用戶是否有任一權限
   */
  async userHasAnyPermission(
    userId: string,
    userType: UserType,
    permissionIds: string[]
  ): Promise<boolean> {
    const effectivePermissions = await this.getUserEffectivePermissions(userId, userType);
    return permissionIds.some((permissionId) => effectivePermissions.includes(permissionId));
  }

  /**
   * 檢查用戶是否有所有權限
   */
  async userHasAllPermissions(
    userId: string,
    userType: UserType,
    permissionIds: string[]
  ): Promise<boolean> {
    const effectivePermissions = await this.getUserEffectivePermissions(userId, userType);
    return permissionIds.every((permissionId) => effectivePermissions.includes(permissionId));
  }

  /**
   * 獲取用戶的角色層級資訊
   */
  async getUserRoleHierarchy(userId: string, userType: UserType): Promise<{
    user: UserRoleInfo;
    roleHierarchies: Array<{
      roleId: string;
      roleName: string;
      level: number;
      parentRoles: string[];
      childRoles: string[];
      directPermissions: string[];
      inheritedPermissions: string[];
      effectivePermissions: string[];
    }>;
  }> {
    const userRoleInfo = await this.roleAssignmentService.getUserRoleInfo(userId, userType);
    const roleHierarchies = [];

    for (const role of userRoleInfo.roles) {
      const hierarchy = await this.roleHierarchyService.getRoleHierarchy(role.id);
      const parentRoles = await this.roleHierarchyService.getAllParentRoles(role.id);
      const childRoles = await this.roleHierarchyService.getAllChildRoles(role.id);
      const effectivePermissions = await this.roleHierarchyService.getEffectivePermissions(role.id);

      roleHierarchies.push({
        roleId: role.id,
        roleName: role.name,
        level: hierarchy.level,
        parentRoles,
        childRoles,
        directPermissions: hierarchy.directPermissions,
        inheritedPermissions: hierarchy.inheritedPermissions,
        effectivePermissions,
      });
    }

    return {
      user: userRoleInfo,
      roleHierarchies,
    };
  }

  /**
   * 根據範圍獲取用戶列表
   */
  async getUsersByScope(scope: RoleScope, tenantId?: string): Promise<UserRoleInfo[]> {
    this.logger.debug(`Getting users by scope: ${scope}, tenant: ${tenantId}`);

    let users: UserRoleInfo[] = [];

    if (scope === RoleScope.SYSTEM) {
      // 獲取系統用戶
      const systemUsers = await this.prisma.system_users.findMany({
        include: {
          system_user_roles: {
            include: {
              role: true,
            },
          },
        },
      });

      users = systemUsers.map((user) => ({
        userId: user.id,
        userType: UserType.SYSTEM,
        user: {
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          status: user.status,
        },
        roles: user.system_user_roles.map((ur) => ({
          id: ur.role.id,
          name: ur.role.name,
          displayName: ur.role.displayName,
          description: ur.role.description || undefined,
          scope: ur.role.scope,
          isSystem: ur.role.isSystem,
          assignedAt: ur.created_at,
        })),
      }));
    } else {
      // 獲取租戶用戶
      const whereClause: any = {};
      if (tenantId) {
        whereClause.tenantId = tenantId;
      }

      const tenantUsers = await this.prisma.tenant_users.findMany({
        where: whereClause,
        include: {
          tenant_user_roles: {
            include: {
              role: true,
            },
          },
          tenants: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      users = tenantUsers.map((user) => ({
        userId: user.id,
        userType: UserType.TENANT,
        user: {
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          status: user.status,
        },
        roles: user.tenant_user_roles.map((ur) => ({
          id: ur.role.id,
          name: ur.role.name,
          displayName: ur.role.displayName,
          description: ur.role.description || undefined,
          scope: ur.role.scope,
          isSystem: ur.role.isSystem,
          assignedAt: ur.created_at,
        })),
        tenant: user.tenants ? {
          id: user.tenants.id,
          name: user.tenants.name,
        } : undefined,
      }));
    }

    this.logger.debug(`Found ${users.length} users with scope ${scope}`);
    return users;
  }

  /**
   * 獲取具有特定角色的用戶
   */
  async getUsersByRole(roleId: string): Promise<UserRoleInfo[]> {
    this.logger.debug(`Getting users with role: ${roleId}`);

    // 驗證角色存在
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`角色 ${roleId} 不存在`);
    }

    const users: UserRoleInfo[] = [];

    // 獲取系統用戶
    const systemUserRoles = await this.prisma.system_user_roles.findMany({
      where: { role_id: roleId },
      include: {
        system_users: true,
        role: true,
      },
    });

    for (const userRole of systemUserRoles) {
      const userRoleInfo = await this.roleAssignmentService.getUserRoleInfo(
        userRole.system_users.id,
        UserType.SYSTEM
      );
      users.push(userRoleInfo);
    }

    // 獲取租戶用戶
    const tenantUserRoles = await this.prisma.tenant_user_roles.findMany({
      where: { role_id: roleId },
      include: {
        tenant_users: true,
        role: true,
      },
    });

    for (const userRole of tenantUserRoles) {
      const userRoleInfo = await this.roleAssignmentService.getUserRoleInfo(
        userRole.tenant_users.id,
        UserType.TENANT
      );
      users.push(userRoleInfo);
    }

    this.logger.debug(`Found ${users.length} users with role ${roleId}`);
    return users;
  }

  /**
   * 獲取具有特定權限的用戶
   */
  async getUsersByPermission(permissionId: string): Promise<UserRoleInfo[]> {
    this.logger.debug(`Getting users with permission: ${permissionId}`);

    // 驗證權限存在
    const permission = await this.prisma.permissions.findUnique({
      where: { id: permissionId },
    });

    if (!permission) {
      throw new NotFoundException(`權限 ${permissionId} 不存在`);
    }

    // 獲取具有此權限的角色
    const rolePermissions = await this.prisma.role_permissions.findMany({
      where: { permissionId },
      select: { roleId: true },
    });

    const roleIds = rolePermissions.map((rp) => rp.roleId);

    if (roleIds.length === 0) {
      return [];
    }

    // 獲取具有這些角色的用戶
    const users: UserRoleInfo[] = [];
    const processedUsers = new Set<string>();

    for (const roleId of roleIds) {
      const roleUsers = await this.getUsersByRole(roleId);
      for (const user of roleUsers) {
        const userKey = `${user.userType}-${user.userId}`;
        if (!processedUsers.has(userKey)) {
          processedUsers.add(userKey);
          users.push(user);
        }
      }
    }

    this.logger.debug(`Found ${users.length} users with permission ${permissionId}`);
    return users;
  }

  /**
   * 同步用戶權限快取
   */
  async syncUserPermissionCache(userId: string, userType: UserType): Promise<void> {
    this.logger.debug(`Syncing permission cache for user ${userId} (${userType})`);

    try {
      // 獲取用戶的有效權限
      const effectivePermissions = await this.getUserEffectivePermissions(userId, userType);

      // 這裡可以實作權限快取邏輯
      // 例如：Redis 快取、資料庫快取表等
      
      this.logger.debug(
        `Synced ${effectivePermissions.length} permissions for user ${userId} (${userType})`
      );
    } catch (error) {
      this.logger.error(
        `Failed to sync permission cache for user ${userId} (${userType}):`,
        error.message
      );
      throw error;
    }
  }

  /**
   * 批量同步用戶權限快取
   */
  async batchSyncUserPermissionCache(
    users: Array<{ userId: string; userType: UserType }>
  ): Promise<{
    successCount: number;
    failureCount: number;
    errors: Array<{ userId: string; userType: UserType; error: string }>;
  }> {
    this.logger.debug(`Batch syncing permission cache for ${users.length} users`);

    let successCount = 0;
    let failureCount = 0;
    const errors: Array<{ userId: string; userType: UserType; error: string }> = [];

    for (const { userId, userType } of users) {
      try {
        await this.syncUserPermissionCache(userId, userType);
        successCount++;
      } catch (error) {
        failureCount++;
        errors.push({
          userId,
          userType,
          error: error.message,
        });
      }
    }

    this.logger.log(
      `Batch sync completed: ${successCount} success, ${failureCount} failures`
    );

    return {
      successCount,
      failureCount,
      errors,
    };
  }
}
