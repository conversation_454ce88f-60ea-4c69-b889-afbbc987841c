import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { RoleAssignmentService } from './role-assignment.service';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { UserType } from '../interfaces/role-assignment.interface';
import { RoleScope } from '@prisma/client';

describe('RoleAssignmentService', () => {
  let service: RoleAssignmentService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    system_users: {
      findUnique: jest.fn(),
    },
    tenant_users: {
      findUnique: jest.fn(),
    },
    roles: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    system_user_roles: {
      findMany: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenant_user_roles: {
      findMany: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenants: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleAssignmentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<RoleAssignmentService>(RoleAssignmentService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('assignRoles', () => {
    const mockSystemUser = {
      id: 'system-user-1',
      email: '<EMAIL>',
      name: 'System Admin',
      status: 'ACTIVE',
    };

    const mockSystemRole = {
      id: 'system-role-1',
      name: 'SYSTEM_ADMIN',
      displayName: '系統管理員',
      scope: RoleScope.SYSTEM,
      isSystem: true,
      tenantId: null,
    };

    it('should successfully assign roles to system user', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([mockSystemRole]);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            findMany: jest.fn().mockResolvedValue([]),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        });
      });

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['system-role-1'],
        reason: 'Initial setup',
        assignedBy: 'admin',
      };

      // Act
      const result = await service.assignRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.userId).toBe('system-user-1');
      expect(result.userType).toBe(UserType.SYSTEM);
      expect(result.assignedRoles).toEqual(['system-role-1']);
      expect(mockPrismaService.system_users.findUnique).toHaveBeenCalledWith({
        where: { id: 'system-user-1' },
      });
      expect(mockPrismaService.roles.findMany).toHaveBeenCalledWith({
        where: { id: { in: ['system-role-1'] } },
      });
    });

    it('should throw NotFoundException when user does not exist', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(null);

      const request = {
        userId: 'non-existent-user',
        userType: UserType.SYSTEM,
        roleIds: ['system-role-1'],
      };

      // Act & Assert
      const result = await service.assignRoles(request);
      expect(result.success).toBe(false);
      expect(result.error).toContain('系統用戶 non-existent-user 不存在');
    });

    it('should throw NotFoundException when role does not exist', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([]); // No roles found

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['non-existent-role'],
      };

      // Act & Assert
      const result = await service.assignRoles(request);
      expect(result.success).toBe(false);
      expect(result.error).toContain('角色不存在');
    });

    it('should throw BadRequestException for scope mismatch', async () => {
      // Arrange
      const tenantRole = {
        ...mockSystemRole,
        id: 'tenant-role-1',
        scope: RoleScope.TENANT,
      };

      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([tenantRole]);

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['tenant-role-1'],
      };

      // Act & Assert
      const result = await service.assignRoles(request);
      expect(result.success).toBe(false);
      expect(result.error).toContain('不適用於系統用戶');
    });
  });

  describe('removeRoles', () => {
    const mockUserRole = {
      id: 'user-role-1',
      system_user_id: 'system-user-1',
      role_id: 'system-role-1',
      role: {
        id: 'system-role-1',
        name: 'SYSTEM_ADMIN',
      },
    };

    it('should successfully remove roles from user', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue({
        id: 'system-user-1',
        tenantId: null,
      });
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([mockUserRole]);
      mockPrismaService.system_user_roles.deleteMany.mockResolvedValue({ count: 1 });

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['system-role-1'],
        reason: 'Role change',
        removedBy: 'admin',
      };

      // Act
      const result = await service.removeRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.removedRoles).toEqual(['system-role-1']);
      expect(mockPrismaService.system_user_roles.deleteMany).toHaveBeenCalledWith({
        where: {
          system_user_id: 'system-user-1',
          role_id: { in: ['system-role-1'] },
        },
      });
    });

    it('should throw BadRequestException when user does not have specified roles', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue({
        id: 'system-user-1',
        tenantId: null,
      });
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([]); // No roles

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['system-role-1'],
      };

      // Act & Assert
      const result = await service.removeRoles(request);
      expect(result.success).toBe(false);
      expect(result.error).toContain('用戶沒有指定的角色');
    });
  });

  describe('replaceRoles', () => {
    it('should successfully replace user roles', async () => {
      // Arrange
      const currentRoles = [
        { id: 'old-role-1', name: 'OLD_ROLE' },
        { id: 'old-role-2', name: 'OLD_ROLE_2' },
      ];

      mockPrismaService.system_user_roles.findMany.mockResolvedValue(
        currentRoles.map(role => ({ role }))
      );
      mockPrismaService.roles.findMany.mockResolvedValue([
        { id: 'new-role-1', scope: RoleScope.SYSTEM },
      ]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            deleteMany: jest.fn(),
            createMany: jest.fn(),
          },
        });
      });

      const request = {
        userId: 'system-user-1',
        userType: UserType.SYSTEM,
        roleIds: ['new-role-1'],
        reason: 'Role restructure',
        assignedBy: 'admin',
      };

      // Act
      const result = await service.replaceRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.assignedRoles).toEqual(['new-role-1']);
      expect(result.removedRoles).toEqual(['old-role-1', 'old-role-2']);
    });
  });

  describe('getUserRoleInfo', () => {
    it('should return user role information', async () => {
      // Arrange
      const mockUser = {
        id: 'system-user-1',
        email: '<EMAIL>',
        name: 'System Admin',
        status: 'ACTIVE',
      };

      const mockUserRoles = [
        {
          role: {
            id: 'system-role-1',
            name: 'SYSTEM_ADMIN',
            displayName: '系統管理員',
            description: '系統管理員角色',
            scope: RoleScope.SYSTEM,
            isSystem: true,
          },
          created_at: new Date(),
        },
      ];

      mockPrismaService.system_users.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue(mockUserRoles);

      // Act
      const result = await service.getUserRoleInfo('system-user-1', UserType.SYSTEM);

      // Assert
      expect(result.userId).toBe('system-user-1');
      expect(result.userType).toBe(UserType.SYSTEM);
      expect(result.user).toEqual(mockUser);
      expect(result.roles).toHaveLength(1);
      expect(result.roles[0].id).toBe('system-role-1');
    });
  });
});
