import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import {
  UserType,
  RoleAssignmentRequest,
  RoleRemovalRequest,
  BatchRoleOperationRequest,
  RoleAssignmentResult,
  BatchRoleOperationResult,
  UserRoleInfo,
  RoleConflictCheck,
} from '../interfaces/role-assignment.interface';
import { RoleScope } from '@prisma/client';

/**
 * 角色指派服務
 * 負責管理用戶角色的指派、移除和驗證
 */
@Injectable()
export class RoleAssignmentService {
  private readonly logger = new Logger(RoleAssignmentService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 指派角色給用戶
   */
  async assignRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult> {
    const { userId, userType, roleIds, tenantId, reason, assignedBy } = request;

    this.logger.debug(`Assigning roles to user ${userId} (${userType}): ${roleIds.join(', ')}`);

    try {
      // 1. 驗證用戶存在
      await this.validateUserExists(userId, userType, tenantId);

      // 2. 驗證角色存在且適用
      await this.validateRoles(roleIds, userType, tenantId);

      // 3. 檢查角色衝突
      const conflictCheck = await this.checkRoleConflicts(userId, userType, roleIds, tenantId);
      if (conflictCheck.hasConflict) {
        throw new ConflictException(
          `角色衝突: ${conflictCheck.conflicts.map((c) => c.message).join(', ')}`
        );
      }

      // 4. 執行角色指派
      const result = await this.executeRoleAssignment(userId, userType, roleIds, tenantId);

      // 5. 記錄變更歷史
      await this.recordRoleChange(
        userId,
        userType,
        'assign',
        [],
        roleIds,
        reason,
        assignedBy,
        tenantId
      );

      this.logger.log(
        `Successfully assigned ${roleIds.length} roles to user ${userId} (${userType})`
      );

      return {
        success: true,
        userId,
        userType,
        assignedRoles: roleIds,
        removedRoles: [],
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to assign roles to user ${userId} (${userType}):`,
        error.message
      );

      return {
        success: false,
        userId,
        userType,
        assignedRoles: [],
        removedRoles: [],
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 移除用戶角色
   */
  async removeRoles(request: RoleRemovalRequest): Promise<RoleAssignmentResult> {
    const { userId, userType, roleIds, reason, removedBy } = request;

    this.logger.debug(
      `Removing roles from user ${userId} (${userType}): ${roleIds.join(', ')}`
    );

    try {
      // 1. 驗證用戶存在
      const tenantId = userType === UserType.TENANT 
        ? await this.getUserTenantId(userId, userType) 
        : undefined;
      
      await this.validateUserExists(userId, userType, tenantId);

      // 2. 獲取用戶當前角色
      const currentRoles = await this.getUserRoles(userId, userType);
      const currentRoleIds = currentRoles.map(r => r.id);

      // 3. 檢查要移除的角色是否存在
      const rolesToRemove = roleIds.filter(roleId => currentRoleIds.includes(roleId));
      if (rolesToRemove.length === 0) {
        throw new BadRequestException('用戶沒有指定的角色');
      }

      // 4. 執行角色移除
      await this.executeRoleRemoval(userId, userType, rolesToRemove);

      // 5. 記錄變更歷史
      await this.recordRoleChange(
        userId,
        userType,
        'remove',
        rolesToRemove,
        [],
        reason,
        removedBy,
        tenantId
      );

      this.logger.log(
        `Successfully removed ${rolesToRemove.length} roles from user ${userId} (${userType})`
      );

      return {
        success: true,
        userId,
        userType,
        assignedRoles: [],
        removedRoles: rolesToRemove,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to remove roles from user ${userId} (${userType}):`,
        error.message
      );

      return {
        success: false,
        userId,
        userType,
        assignedRoles: [],
        removedRoles: [],
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 替換用戶角色
   */
  async replaceRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult> {
    const { userId, userType, roleIds, tenantId, reason, assignedBy } = request;

    this.logger.debug(
      `Replacing roles for user ${userId} (${userType}) with: ${roleIds.join(', ')}`
    );

    try {
      // 1. 獲取用戶當前角色
      const currentRoles = await this.getUserRoles(userId, userType);
      const currentRoleIds = currentRoles.map(r => r.id);

      // 2. 在事務中執行替換
      const result = await this.prisma.$transaction(async (tx) => {
        // 移除所有現有角色
        if (currentRoleIds.length > 0) {
          await this.executeRoleRemovalInTransaction(tx, userId, userType, currentRoleIds);
        }

        // 指派新角色
        if (roleIds.length > 0) {
          await this.validateRoles(roleIds, userType, tenantId);
          await this.executeRoleAssignmentInTransaction(tx, userId, userType, roleIds, tenantId);
        }

        return { currentRoleIds, newRoleIds: roleIds };
      });

      // 3. 記錄變更歷史
      await this.recordRoleChange(
        userId,
        userType,
        'replace',
        result.currentRoleIds,
        result.newRoleIds,
        reason,
        assignedBy,
        tenantId
      );

      this.logger.log(
        `Successfully replaced roles for user ${userId} (${userType})`
      );

      return {
        success: true,
        userId,
        userType,
        assignedRoles: roleIds,
        removedRoles: currentRoleIds,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to replace roles for user ${userId} (${userType}):`,
        error.message
      );

      return {
        success: false,
        userId,
        userType,
        assignedRoles: [],
        removedRoles: [],
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 批量角色操作
   */
  async batchRoleOperations(request: BatchRoleOperationRequest): Promise<BatchRoleOperationResult> {
    const { operations, reason, operatedBy } = request;

    this.logger.debug(`Executing batch role operations: ${operations.length} operations`);

    const results: RoleAssignmentResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const operation of operations) {
      try {
        let result: RoleAssignmentResult;

        switch (operation.action) {
          case 'assign':
            result = await this.assignRoles({
              userId: operation.userId,
              userType: operation.userType,
              roleIds: operation.roleIds,
              tenantId: operation.tenantId,
              reason,
              assignedBy: operatedBy,
            });
            break;

          case 'remove':
            result = await this.removeRoles({
              userId: operation.userId,
              userType: operation.userType,
              roleIds: operation.roleIds,
              reason,
              removedBy: operatedBy,
            });
            break;

          case 'replace':
            result = await this.replaceRoles({
              userId: operation.userId,
              userType: operation.userType,
              roleIds: operation.roleIds,
              tenantId: operation.tenantId,
              reason,
              assignedBy: operatedBy,
            });
            break;

          default:
            throw new BadRequestException(`不支援的操作類型: ${operation.action}`);
        }

        results.push(result);
        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        results.push({
          success: false,
          userId: operation.userId,
          userType: operation.userType,
          assignedRoles: [],
          removedRoles: [],
          error: error.message,
          timestamp: new Date(),
        });
        failureCount++;
      }
    }

    this.logger.log(
      `Batch role operations completed: ${successCount} success, ${failureCount} failures`
    );

    return {
      totalOperations: operations.length,
      successCount,
      failureCount,
      results,
      timestamp: new Date(),
    };
  }

  /**
   * 獲取用戶角色資訊
   */
  async getUserRoleInfo(userId: string, userType: UserType): Promise<UserRoleInfo> {
    const tenantId = userType === UserType.TENANT
      ? await this.getUserTenantId(userId, userType)
      : undefined;

    // 驗證用戶存在
    await this.validateUserExists(userId, userType, tenantId);

    // 獲取用戶基本資訊
    const user = await this.getUserBasicInfo(userId, userType);

    // 獲取用戶角色
    const roles = await this.getUserRolesWithDetails(userId, userType);

    // 獲取租戶資訊（如果是租戶用戶）
    let tenant;
    if (userType === UserType.TENANT && tenantId) {
      tenant = await this.prisma.tenants.findUnique({
        where: { id: tenantId },
        select: { id: true, name: true },
      });
    }

    return {
      userId,
      userType,
      user,
      roles,
      tenant,
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 驗證用戶是否存在
   */
  private async validateUserExists(
    userId: string,
    userType: UserType,
    tenantId?: string
  ): Promise<void> {
    if (userType === UserType.SYSTEM) {
      const user = await this.prisma.system_users.findUnique({
        where: { id: userId },
      });
      if (!user) {
        throw new NotFoundException(`系統用戶 ${userId} 不存在`);
      }
    } else {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
      });
      if (!user) {
        throw new NotFoundException(`租戶用戶 ${userId} 不存在`);
      }
      if (tenantId && user.tenantId !== tenantId) {
        throw new BadRequestException(`用戶 ${userId} 不屬於租戶 ${tenantId}`);
      }
    }
  }

  /**
   * 驗證角色是否存在且適用
   */
  private async validateRoles(
    roleIds: string[],
    userType: UserType,
    tenantId?: string
  ): Promise<void> {
    const roles = await this.prisma.roles.findMany({
      where: { id: { in: roleIds } },
    });

    if (roles.length !== roleIds.length) {
      const foundRoleIds = roles.map(r => r.id);
      const missingRoleIds = roleIds.filter(id => !foundRoleIds.includes(id));
      throw new NotFoundException(`角色不存在: ${missingRoleIds.join(', ')}`);
    }

    // 檢查角色範圍是否適用
    for (const role of roles) {
      if (userType === UserType.SYSTEM) {
        if (role.scope !== RoleScope.SYSTEM) {
          throw new BadRequestException(
            `角色 ${role.name} (${role.scope}) 不適用於系統用戶`
          );
        }
      } else {
        if (role.scope === RoleScope.SYSTEM) {
          throw new BadRequestException(
            `角色 ${role.name} (${role.scope}) 不適用於租戶用戶`
          );
        }
        if ((role.scope === RoleScope.TENANT || role.scope === RoleScope.WORKSPACE) &&
            role.tenantId && role.tenantId !== tenantId) {
          throw new BadRequestException(
            `角色 ${role.name} 不屬於租戶 ${tenantId}`
          );
        }
      }
    }
  }

  /**
   * 檢查角色衝突
   */
  private async checkRoleConflicts(
    userId: string,
    userType: UserType,
    newRoleIds: string[],
    tenantId?: string
  ): Promise<RoleConflictCheck> {
    const conflicts: RoleConflictCheck['conflicts'] = [];
    const suggestions: string[] = [];

    // 獲取當前角色
    const currentRoles = await this.getUserRoles(userId, userType);
    const allRoleIds = [...currentRoles.map(r => r.id), ...newRoleIds];

    // 獲取所有相關角色的詳細資訊
    const allRoles = await this.prisma.roles.findMany({
      where: { id: { in: allRoleIds } },
      include: { roles: true }, // 父角色
    });

    // 檢查範圍衝突
    const scopes = [...new Set(allRoles.map(r => r.scope))];
    if (scopes.length > 1 && scopes.includes(RoleScope.SYSTEM)) {
      conflicts.push({
        type: 'scope_mismatch',
        message: '不能同時擁有系統角色和非系統角色',
        conflictingRoles: allRoles.filter(r => r.scope === RoleScope.SYSTEM).map(r => r.id),
      });
    }

    // 檢查租戶匹配
    if (userType === UserType.TENANT && tenantId) {
      const tenantMismatchRoles = allRoles.filter(
        r => r.scope !== RoleScope.SYSTEM && r.tenantId && r.tenantId !== tenantId
      );
      if (tenantMismatchRoles.length > 0) {
        conflicts.push({
          type: 'tenant_mismatch',
          message: '角色不屬於用戶的租戶',
          conflictingRoles: tenantMismatchRoles.map(r => r.id),
        });
      }
    }

    return {
      hasConflict: conflicts.length > 0,
      conflicts,
      suggestions,
    };
  }

  /**
   * 執行角色指派
   */
  private async executeRoleAssignment(
    userId: string,
    userType: UserType,
    roleIds: string[],
    tenantId?: string
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      await this.executeRoleAssignmentInTransaction(tx, userId, userType, roleIds, tenantId);
    });
  }

  /**
   * 在事務中執行角色指派
   */
  private async executeRoleAssignmentInTransaction(
    tx: any,
    userId: string,
    userType: UserType,
    roleIds: string[],
    tenantId?: string
  ): Promise<void> {
    if (userType === UserType.SYSTEM) {
      // 檢查已存在的角色指派
      const existingAssignments = await tx.system_user_roles.findMany({
        where: {
          system_user_id: userId,
          role_id: { in: roleIds },
        },
      });

      const existingRoleIds = existingAssignments.map((a: any) => a.role_id);
      const newRoleIds = roleIds.filter(id => !existingRoleIds.includes(id));

      if (newRoleIds.length > 0) {
        await tx.system_user_roles.createMany({
          data: newRoleIds.map(roleId => ({
            id: `${userId}-${roleId}-${Date.now()}`,
            system_user_id: userId,
            role_id: roleId,
          })),
        });
      }
    } else {
      // 檢查已存在的角色指派
      const existingAssignments = await tx.tenant_user_roles.findMany({
        where: {
          tenant_user_id: userId,
          role_id: { in: roleIds },
        },
      });

      const existingRoleIds = existingAssignments.map((a: any) => a.role_id);
      const newRoleIds = roleIds.filter(id => !existingRoleIds.includes(id));

      if (newRoleIds.length > 0) {
        await tx.tenant_user_roles.createMany({
          data: newRoleIds.map(roleId => ({
            id: `${userId}-${roleId}-${Date.now()}`,
            tenant_user_id: userId,
            role_id: roleId,
          })),
        });
      }
    }
  }

  /**
   * 執行角色移除
   */
  private async executeRoleRemoval(
    userId: string,
    userType: UserType,
    roleIds: string[]
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      await this.executeRoleRemovalInTransaction(tx, userId, userType, roleIds);
    });
  }

  /**
   * 在事務中執行角色移除
   */
  private async executeRoleRemovalInTransaction(
    tx: any,
    userId: string,
    userType: UserType,
    roleIds: string[]
  ): Promise<void> {
    if (userType === UserType.SYSTEM) {
      await tx.system_user_roles.deleteMany({
        where: {
          system_user_id: userId,
          role_id: { in: roleIds },
        },
      });
    } else {
      await tx.tenant_user_roles.deleteMany({
        where: {
          tenant_user_id: userId,
          role_id: { in: roleIds },
        },
      });
    }
  }

  /**
   * 獲取用戶角色
   */
  private async getUserRoles(userId: string, userType: UserType): Promise<any[]> {
    if (userType === UserType.SYSTEM) {
      const userRoles = await this.prisma.system_user_roles.findMany({
        where: { system_user_id: userId },
        include: { role: true },
      });
      return userRoles.map(ur => ur.role);
    } else {
      const userRoles = await this.prisma.tenant_user_roles.findMany({
        where: { tenant_user_id: userId },
        include: { role: true },
      });
      return userRoles.map(ur => ur.role);
    }
  }

  /**
   * 獲取用戶角色詳細資訊
   */
  private async getUserRolesWithDetails(userId: string, userType: UserType): Promise<any[]> {
    if (userType === UserType.SYSTEM) {
      const userRoles = await this.prisma.system_user_roles.findMany({
        where: { system_user_id: userId },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              scope: true,
              isSystem: true,
            },
          },
        },
        orderBy: { created_at: 'asc' },
      });
      return userRoles.map(ur => ({
        ...ur.role,
        assignedAt: ur.created_at,
      }));
    } else {
      const userRoles = await this.prisma.tenant_user_roles.findMany({
        where: { tenant_user_id: userId },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              scope: true,
              isSystem: true,
            },
          },
        },
        orderBy: { created_at: 'asc' },
      });
      return userRoles.map(ur => ({
        ...ur.role,
        assignedAt: ur.created_at,
      }));
    }
  }

  /**
   * 獲取用戶基本資訊
   */
  private async getUserBasicInfo(userId: string, userType: UserType): Promise<any> {
    if (userType === UserType.SYSTEM) {
      const user = await this.prisma.system_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          status: true,
        },
      });
      return user;
    } else {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          status: true,
        },
      });
      return user;
    }
  }

  /**
   * 獲取用戶的租戶 ID
   */
  private async getUserTenantId(userId: string, userType: UserType): Promise<string | undefined> {
    if (userType === UserType.TENANT) {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { tenantId: true },
      });
      return user?.tenantId;
    }
    return undefined;
  }

  /**
   * 記錄角色變更歷史
   */
  private async recordRoleChange(
    userId: string,
    userType: UserType,
    changeType: 'assign' | 'remove' | 'replace',
    previousRoles: string[],
    newRoles: string[],
    reason?: string,
    changedBy?: string,
    tenantId?: string
  ): Promise<void> {
    // 這裡可以實作角色變更歷史記錄
    // 由於沒有專門的歷史表，我們可以使用日誌記錄
    this.logger.log(
      `Role change recorded: User ${userId} (${userType}) - ${changeType} - ` +
      `Previous: [${previousRoles.join(', ')}] -> New: [${newRoles.join(', ')}] - ` +
      `Reason: ${reason || 'N/A'} - Changed by: ${changedBy || 'System'} - ` +
      `Tenant: ${tenantId || 'N/A'}`
    );
  }
}
