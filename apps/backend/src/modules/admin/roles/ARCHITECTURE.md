# 角色管理模組架構文檔

## 📋 概覽

本文檔說明角色管理模組的最終架構，整合了現有的角色定義管理和新增的角色指派管理功能。

## 🏗️ 架構設計

### 職責分離原則

```
角色管理模組 (RolesModule)
├── 角色定義管理 (Role Definition Management)
│   ├── RolesController - 角色 CRUD API
│   └── RolesService - 角色定義業務邏輯
└── 角色指派管理 (Role Assignment Management)
    ├── RoleAssignmentController - 角色指派 API
    ├── RoleAssignmentService - 角色指派核心邏輯
    ├── RoleHierarchyService - 角色層級管理
    └── UserRoleService - 用戶角色查詢
```

## 📁 檔案結構

```
apps/backend/src/modules/admin/roles/
├── controllers/
│   ├── roles.controller.ts              # 角色定義管理 API
│   └── role-assignment.controller.ts    # 角色指派管理 API
├── services/
│   ├── roles.service.ts                 # 角色定義服務
│   ├── role-assignment.service.ts       # 角色指派服務
│   ├── role-hierarchy.service.ts        # 角色層級服務
│   └── user-role.service.ts             # 用戶角色服務
├── dto/
│   ├── role.dto.ts                      # 角色定義 DTO
│   └── role-assignment.dto.ts           # 角色指派 DTO
├── interfaces/
│   └── role-assignment.interface.ts     # 角色指派介面
├── examples/
│   └── role-management.example.ts       # 使用範例
├── README.md                            # 使用文檔
├── ARCHITECTURE.md                      # 架構文檔 (本檔案)
└── roles.module.ts                      # 模組定義
```

## 🎯 服務職責

### 1. RolesService (角色定義服務)

**主要職責：**
- 角色的建立、讀取、更新、刪除
- 權限定義和分類管理
- 角色權限關聯管理
- 角色定義驗證和業務邏輯

**核心方法：**
```typescript
// 角色 CRUD
findAll(scope?: RoleScope): Promise<Role[]>
findOne(id: string): Promise<RoleWithPermissions>
create(createRoleDto: CreateRoleDto): Promise<Role>
update(id: string, updateRoleDto: UpdateRoleDto): Promise<Role>
remove(id: string): Promise<{ success: boolean; message: string }>

// 權限管理
getAllPermissions(): Promise<PermissionDto[]>
getAllPermissionCategories(): Promise<PermissionCategoryDto[]>
updateRolePermissions(roleId: string, permissionIds: string[]): Promise<Result>

// 統計功能
getUserCount(roleId: string): Promise<number>
```

### 2. RoleAssignmentService (角色指派服務)

**主要職責：**
- 用戶角色指派、移除、替換
- 批量角色操作
- 角色衝突檢測
- 角色指派驗證

**核心方法：**
```typescript
assignRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult>
removeRoles(request: RoleRemovalRequest): Promise<RoleAssignmentResult>
replaceRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult>
batchRoleOperations(request: BatchRoleOperationRequest): Promise<BatchRoleOperationResult>
getUserRoleInfo(userId: string, userType: UserType): Promise<UserRoleInfo>
```

### 3. RoleHierarchyService (角色層級服務)

**主要職責：**
- 角色父子關係管理
- 權限繼承計算
- 角色層級驗證
- 循環引用檢測

**核心方法：**
```typescript
getRoleHierarchy(roleId: string): Promise<RoleHierarchyInfo>
setParentRole(roleId: string, parentRoleId: string | null): Promise<void>
getAllChildRoles(roleId: string): Promise<string[]>
getAllParentRoles(roleId: string): Promise<string[]>
getInheritedPermissions(roleId: string): Promise<string[]>
getEffectivePermissions(roleId: string): Promise<string[]>
validateHierarchy(): Promise<ValidationResult>
```

### 4. UserRoleService (用戶角色服務)

**主要職責：**
- 用戶有效權限計算
- 用戶權限檢查
- 用戶角色查詢
- 權限快取管理

**核心方法：**
```typescript
getUserEffectivePermissions(userId: string, userType: UserType): Promise<string[]>
userHasPermission(userId: string, userType: UserType, permissionId: string): Promise<boolean>
userHasAnyPermission(userId: string, userType: UserType, permissionIds: string[]): Promise<boolean>
userHasAllPermissions(userId: string, userType: UserType, permissionIds: string[]): Promise<boolean>
getUsersByScope(scope: RoleScope, tenantId?: string): Promise<UserRoleInfo[]>
getUsersByRole(roleId: string): Promise<UserRoleInfo[]>
getUsersByPermission(permissionId: string): Promise<UserRoleInfo[]>
syncUserPermissionCache(userId: string, userType: UserType): Promise<void>
```

## 🔗 API 端點分組

### 角色定義管理 API (`/admin/roles`)

```
GET    /admin/roles                    # 獲取所有角色
GET    /admin/roles/:id                # 獲取角色詳情
POST   /admin/roles                    # 建立角色
PUT    /admin/roles/:id                # 更新角色
DELETE /admin/roles/:id                # 刪除角色
PUT    /admin/roles/:id/permissions    # 更新角色權限
GET    /admin/roles/:id/users/count    # 獲取角色用戶數量
GET    /admin/roles/permissions        # 獲取所有權限
GET    /admin/roles/permissions/categories # 獲取權限分類
```

### 角色指派管理 API (`/admin/role-assignment`)

```
POST   /admin/role-assignment/assign           # 指派角色
POST   /admin/role-assignment/remove           # 移除角色
PUT    /admin/role-assignment/replace          # 替換角色
POST   /admin/role-assignment/batch            # 批量操作
GET    /admin/role-assignment/user/:userId/:userType # 獲取用戶角色
GET    /admin/role-assignment/user/:userId/:userType/hierarchy # 獲取用戶角色層級
GET    /admin/role-assignment/user/:userId/:userType/permissions # 獲取用戶權限
POST   /admin/role-assignment/check-permission # 檢查用戶權限
POST   /admin/role-assignment/check-permissions # 批量檢查權限
GET    /admin/role-assignment/users/by-scope/:scope # 按範圍獲取用戶
GET    /admin/role-assignment/users/by-role/:roleId # 按角色獲取用戶
GET    /admin/role-assignment/users/by-permission/:permissionId # 按權限獲取用戶
PUT    /admin/role-assignment/hierarchy/parent # 設置父角色
GET    /admin/role-assignment/hierarchy/:roleId # 獲取角色層級
GET    /admin/role-assignment/hierarchy/validate # 驗證層級結構
POST   /admin/role-assignment/sync-cache/:userId/:userType # 同步權限快取
```

## 🔄 整合策略

### 1. 保持向後相容性

現有的 API 端點和功能保持不變，確保前端應用程式無需修改即可繼續使用。

### 2. 漸進式遷移

- **第一階段**：保留現有功能，新增角色指派功能
- **第二階段**：逐步將複雜的角色管理邏輯遷移到新服務
- **第三階段**：最佳化和整合，移除重複程式碼

### 3. 服務間協作

```typescript
// 範例：RoleAssignmentService 使用 RolesService 驗證角色
@Injectable()
export class RoleAssignmentService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly rolesService: RolesService // 注入現有服務
  ) {}

  private async validateRoles(roleIds: string[]) {
    // 使用 RolesService 驗證角色存在性
    for (const roleId of roleIds) {
      await this.rolesService.findOne(roleId);
    }
  }
}
```

## 🚨 最佳實踐

### 1. 錯誤處理

- 統一的錯誤類型和訊息
- 詳細的日誌記錄
- 適當的 HTTP 狀態碼

### 2. 效能最佳化

- 批量操作減少資料庫查詢
- 權限快取機制
- 分頁查詢大量資料

### 3. 安全考量

- 租戶隔離驗證
- 權限檢查
- 輸入驗證和清理

### 4. 測試策略

- 單元測試覆蓋所有服務
- 整合測試驗證 API 功能
- 端到端測試確保完整流程

## 📊 監控和指標

### 關鍵指標

- 角色指派成功率
- 權限檢查響應時間
- 角色衝突檢測次數
- API 調用頻率和錯誤率

### 日誌記錄

- 所有角色變更操作
- 權限檢查結果
- 錯誤和異常情況
- 效能指標

## 🔮 未來擴展

### 計劃中的功能

1. **角色模板系統** - 預定義的角色模板
2. **動態權限** - 基於條件的動態權限計算
3. **角色審批流程** - 角色變更的審批機制
4. **權限分析報告** - 詳細的權限使用分析
5. **角色生命週期管理** - 自動化的角色管理

### 技術改進

1. **GraphQL 支援** - 提供 GraphQL API
2. **事件驅動架構** - 角色變更事件通知
3. **微服務拆分** - 將角色管理獨立為微服務
4. **AI 輔助** - 智能角色推薦和異常檢測

這個架構設計確保了現有功能的穩定性，同時為未來的擴展提供了靈活的基礎。
