import { RoleScope } from '@prisma/client';

/**
 * 用戶類型枚舉
 */
export enum UserType {
  SYSTEM = 'system',
  TENANT = 'tenant',
}

/**
 * 角色指派請求介面
 */
export interface RoleAssignmentRequest {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 角色 ID 陣列
   */
  roleIds: string[];
  
  /**
   * 租戶 ID（租戶用戶必填）
   */
  tenantId?: string;
  
  /**
   * 指派原因
   */
  reason?: string;
  
  /**
   * 指派者 ID
   */
  assignedBy?: string;
}

/**
 * 角色移除請求介面
 */
export interface RoleRemovalRequest {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 要移除的角色 ID 陣列
   */
  roleIds: string[];
  
  /**
   * 移除原因
   */
  reason?: string;
  
  /**
   * 移除者 ID
   */
  removedBy?: string;
}

/**
 * 批量角色操作請求介面
 */
export interface BatchRoleOperationRequest {
  /**
   * 用戶操作陣列
   */
  operations: Array<{
    userId: string;
    userType: UserType;
    action: 'assign' | 'remove' | 'replace';
    roleIds: string[];
    tenantId?: string;
  }>;
  
  /**
   * 操作原因
   */
  reason?: string;
  
  /**
   * 操作者 ID
   */
  operatedBy?: string;
}

/**
 * 角色指派結果介面
 */
export interface RoleAssignmentResult {
  /**
   * 是否成功
   */
  success: boolean;
  
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 指派的角色 ID 陣列
   */
  assignedRoles: string[];
  
  /**
   * 移除的角色 ID 陣列
   */
  removedRoles: string[];
  
  /**
   * 錯誤訊息（如果有）
   */
  error?: string;
  
  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 批量角色操作結果介面
 */
export interface BatchRoleOperationResult {
  /**
   * 總操作數
   */
  totalOperations: number;
  
  /**
   * 成功操作數
   */
  successCount: number;
  
  /**
   * 失敗操作數
   */
  failureCount: number;
  
  /**
   * 個別操作結果
   */
  results: RoleAssignmentResult[];
  
  /**
   * 整體錯誤訊息（如果有）
   */
  error?: string;
  
  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 用戶角色資訊介面
 */
export interface UserRoleInfo {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 用戶基本資訊
   */
  user: {
    id: string;
    email: string;
    name?: string;
    status: string;
  };
  
  /**
   * 角色陣列
   */
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
    description?: string;
    scope: RoleScope;
    isSystem: boolean;
    assignedAt: Date;
  }>;
  
  /**
   * 租戶資訊（租戶用戶）
   */
  tenant?: {
    id: string;
    name: string;
  };
}

/**
 * 角色衝突檢查結果介面
 */
export interface RoleConflictCheck {
  /**
   * 是否有衝突
   */
  hasConflict: boolean;
  
  /**
   * 衝突詳情
   */
  conflicts: Array<{
    type: 'scope_mismatch' | 'hierarchy_violation' | 'permission_conflict' | 'tenant_mismatch';
    message: string;
    conflictingRoles: string[];
  }>;
  
  /**
   * 建議
   */
  suggestions: string[];
}

/**
 * 角色層級資訊介面
 */
export interface RoleHierarchyInfo {
  /**
   * 角色 ID
   */
  roleId: string;
  
  /**
   * 角色名稱
   */
  roleName: string;
  
  /**
   * 父角色 ID
   */
  parentRoleId?: string;
  
  /**
   * 子角色 ID 陣列
   */
  childRoleIds: string[];
  
  /**
   * 層級深度
   */
  level: number;
  
  /**
   * 繼承的權限
   */
  inheritedPermissions: string[];
  
  /**
   * 直接權限
   */
  directPermissions: string[];
}

/**
 * 角色變更歷史介面
 */
export interface RoleChangeHistory {
  /**
   * 變更 ID
   */
  id: string;
  
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 變更類型
   */
  changeType: 'assign' | 'remove' | 'replace';
  
  /**
   * 變更前的角色
   */
  previousRoles: string[];
  
  /**
   * 變更後的角色
   */
  newRoles: string[];
  
  /**
   * 變更原因
   */
  reason?: string;
  
  /**
   * 變更者 ID
   */
  changedBy: string;
  
  /**
   * 變更時間
   */
  changedAt: Date;
  
  /**
   * 租戶 ID（如果適用）
   */
  tenantId?: string;
}
