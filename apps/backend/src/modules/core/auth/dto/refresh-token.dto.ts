import { IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RefreshTokenDto {
  @ApiProperty({
    description: 'The refresh token used to obtain a new access token. Optional when using cookie-based authentication.',
    example: 'a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8',
    required: false,
  })
  @IsString()
  @IsOptional()
  refreshToken?: string;
}